    </div> <!-- End main-content -->
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.js"></script>
    
    <script>
        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('collapsed');
        });
        
        // Mobile sidebar toggle
        if (window.innerWidth <= 768) {
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('show');
            });
        }
        
        // Initialize Summernote for content editors
        $(document).ready(function() {
            $('.summernote').summernote({
                height: 300,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks: {
                    onImageUpload: function(files) {
                        uploadImage(files[0], this);
                    }
                }
            });
        });
        
        // Image upload for Summernote
        function uploadImage(file, editor) {
            var formData = new FormData();
            formData.append('file', file);
            formData.append('action', 'upload_image');
            
            $.ajax({
                url: 'ajax/upload.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    var data = JSON.parse(response);
                    if (data.success) {
                        $(editor).summernote('insertImage', data.url);
                    } else {
                        alert('Upload failed: ' + data.message);
                    }
                },
                error: function() {
                    alert('Upload failed');
                }
            });
        }
        
        // Confirm delete actions
        function confirmDelete(message) {
            return confirm(message || 'Are you sure you want to delete this item?');
        }
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
        
        // Form validation
        function validateForm(formId) {
            var form = document.getElementById(formId);
            var inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
            var valid = true;
            
            inputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    valid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            return valid;
        }
        
        // Slug generation
        function generateSlug(text, targetId) {
            var slug = text.toLowerCase()
                .replace(/[^\w\s-]/g, '') // Remove special characters
                .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
                .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
            
            document.getElementById(targetId).value = slug;
        }
        
        // Character counter
        function updateCharCount(inputId, counterId, maxLength) {
            var input = document.getElementById(inputId);
            var counter = document.getElementById(counterId);
            var remaining = maxLength - input.value.length;
            
            counter.textContent = remaining + ' characters remaining';
            
            if (remaining < 0) {
                counter.classList.add('text-danger');
                input.classList.add('is-invalid');
            } else {
                counter.classList.remove('text-danger');
                input.classList.remove('is-invalid');
            }
        }
        
        // AJAX form submission
        function submitAjaxForm(formId, successCallback) {
            var form = document.getElementById(formId);
            var formData = new FormData(form);
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (successCallback) {
                        successCallback(data);
                    } else {
                        location.reload();
                    }
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });
        }
        
        // Data tables initialization
        $(document).ready(function() {
            if ($.fn.DataTable) {
                $('.data-table').DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'desc']],
                    language: {
                        search: "Search:",
                        lengthMenu: "Show _MENU_ entries",
                        info: "Showing _START_ to _END_ of _TOTAL_ entries",
                        paginate: {
                            first: "First",
                            last: "Last",
                            next: "Next",
                            previous: "Previous"
                        }
                    }
                });
            }
        });
        
        // File upload preview
        function previewImage(input, previewId) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById(previewId).src = e.target.result;
                    document.getElementById(previewId).style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                var toast = document.createElement('div');
                toast.className = 'toast position-fixed top-0 end-0 m-3';
                toast.innerHTML = `
                    <div class="toast-body bg-success text-white">
                        Copied to clipboard!
                    </div>
                `;
                document.body.appendChild(toast);
                
                var bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                setTimeout(function() {
                    document.body.removeChild(toast);
                }, 3000);
            });
        }
        
        // Auto-save functionality
        var autoSaveTimer;
        function enableAutoSave(formId, interval = 30000) {
            clearInterval(autoSaveTimer);
            autoSaveTimer = setInterval(function() {
                var form = document.getElementById(formId);
                var formData = new FormData(form);
                formData.append('auto_save', '1');
                
                fetch('ajax/auto_save.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show auto-save indicator
                        var indicator = document.getElementById('auto-save-indicator');
                        if (indicator) {
                            indicator.textContent = 'Auto-saved at ' + new Date().toLocaleTimeString();
                            indicator.classList.add('text-success');
                        }
                    }
                })
                .catch(error => {
                    console.error('Auto-save error:', error);
                });
            }, interval);
        }
    </script>
    
    <!-- Custom page scripts -->
    <?php if (isset($custom_scripts)): ?>
        <?= $custom_scripts ?>
    <?php endif; ?>
    
</body>
</html>
