<?php
/**
 * Tags Management
 * Bengali News CMS
 */

require_once '../config/config.php';
requireLogin();

if (!hasPermission('editor')) {
    showMessage('You do not have permission to manage tags', 'error');
    redirect(ADMIN_URL . '/dashboard.php');
}

$action = $_GET['action'] ?? 'list';
$tag_id = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token';
    } else {
        if ($action === 'add' || $action === 'edit') {
            $name = cleanInput($_POST['name']);
            $name_bn = cleanInput($_POST['name_bn']);
            $description = cleanInput($_POST['description']);
            
            // Generate slug
            $slug = generateSlug($name_bn ?: $name);
            
            if ($action === 'add') {
                // Check if slug already exists
                $stmt = $db->prepare("SELECT id FROM tags WHERE slug = ?");
                $stmt->execute([$slug]);
                if ($stmt->fetch()) {
                    $slug .= '-' . time();
                }
                
                $sql = "INSERT INTO tags (name, name_bn, slug, description) VALUES (?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $result = $stmt->execute([$name, $name_bn, $slug, $description]);
                
                if ($result) {
                    $success = 'Tag created successfully';
                } else {
                    $error = 'Failed to create tag';
                }
                
            } elseif ($action === 'edit' && $tag_id) {
                // Check if slug already exists (excluding current tag)
                $stmt = $db->prepare("SELECT id FROM tags WHERE slug = ? AND id != ?");
                $stmt->execute([$slug, $tag_id]);
                if ($stmt->fetch()) {
                    $slug .= '-' . time();
                }
                
                $sql = "UPDATE tags SET name = ?, name_bn = ?, slug = ?, description = ? WHERE id = ?";
                $stmt = $db->prepare($sql);
                $result = $stmt->execute([$name, $name_bn, $slug, $description, $tag_id]);
                
                if ($result) {
                    $success = 'Tag updated successfully';
                } else {
                    $error = 'Failed to update tag';
                }
            }
        } elseif ($action === 'delete' && $tag_id) {
            // Check if tag has posts
            $stmt = $db->prepare("SELECT COUNT(*) as post_count FROM post_tags WHERE tag_id = ?");
            $stmt->execute([$tag_id]);
            $result = $stmt->fetch();
            
            if ($result['post_count'] > 0) {
                $error = 'Cannot delete tag with existing posts';
            } else {
                $stmt = $db->prepare("DELETE FROM tags WHERE id = ?");
                if ($stmt->execute([$tag_id])) {
                    $success = 'Tag deleted successfully';
                } else {
                    $error = 'Failed to delete tag';
                }
            }
        } elseif ($action === 'bulk_delete') {
            $tag_ids = $_POST['tag_ids'] ?? [];
            if (!empty($tag_ids)) {
                $placeholders = str_repeat('?,', count($tag_ids) - 1) . '?';
                
                // Check if any tags have posts
                $stmt = $db->prepare("SELECT DISTINCT tag_id FROM post_tags WHERE tag_id IN ($placeholders)");
                $stmt->execute($tag_ids);
                $used_tags = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $deletable_tags = array_diff($tag_ids, $used_tags);
                
                if (!empty($deletable_tags)) {
                    $placeholders = str_repeat('?,', count($deletable_tags) - 1) . '?';
                    $stmt = $db->prepare("DELETE FROM tags WHERE id IN ($placeholders)");
                    $stmt->execute($deletable_tags);
                    
                    $deleted_count = count($deletable_tags);
                    $used_count = count($used_tags);
                    
                    if ($used_count > 0) {
                        $success = "Deleted $deleted_count tags. $used_count tags with posts were not deleted.";
                    } else {
                        $success = "Deleted $deleted_count tags successfully.";
                    }
                } else {
                    $error = 'Cannot delete tags that are in use by posts';
                }
            }
        }
    }
}

// Get tag data for edit
$tag_data = null;
if ($action === 'edit' && $tag_id) {
    $stmt = $db->prepare("SELECT * FROM tags WHERE id = ?");
    $stmt->execute([$tag_id]);
    $tag_data = $stmt->fetch();
}

$page_title = ($action === 'add') ? 'Add Tag' : (($action === 'edit') ? 'Edit Tag' : 'Tags');
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($action === 'list'): ?>
        <!-- Tags List -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Tags</h1>
                    <div>
                        <button type="button" class="btn btn-danger me-2" id="bulkDeleteBtn" style="display: none;">
                            <i class="fas fa-trash me-2"></i>Delete Selected
                        </button>
                        <a href="tags.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add New Tag
                        </a>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <?php
                        // Get all tags with post count
                        $stmt = $db->prepare("SELECT t.*, COUNT(pt.post_id) as post_count 
                                             FROM tags t 
                                             LEFT JOIN post_tags pt ON t.id = pt.tag_id 
                                             GROUP BY t.id 
                                             ORDER BY t.name_bn ASC");
                        $stmt->execute();
                        $tags = $stmt->fetchAll();
                        ?>
                        
                        <?php if (empty($tags)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-hashtag fa-3x text-muted mb-3"></i>
                                <h5>No tags found</h5>
                                <p class="text-muted">Tags help organize and categorize your content</p>
                                <a href="tags.php?action=add" class="btn btn-primary">Add New Tag</a>
                            </div>
                        <?php else: ?>
                            <form id="bulkForm" method="POST">
                                <?= getCSRFTokenInput() ?>
                                <input type="hidden" name="action" value="bulk_delete">
                                
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th width="30">
                                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                                </th>
                                                <th>Tag Name</th>
                                                <th>Slug</th>
                                                <th>Posts</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($tags as $tag): ?>
                                                <tr>
                                                    <td>
                                                        <input type="checkbox" name="tag_ids[]" value="<?= $tag['id'] ?>" 
                                                               class="form-check-input tag-checkbox">
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <div class="fw-bold"><?= e($tag['name_bn']) ?></div>
                                                            <?php if ($tag['name']): ?>
                                                                <small class="text-muted"><?= e($tag['name']) ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php if ($tag['description']): ?>
                                                            <div class="small text-muted mt-1"><?= e(truncateText($tag['description'], 60)) ?></div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <code><?= e($tag['slug']) ?></code>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><?= number_format($tag['post_count']) ?></span>
                                                    </td>
                                                    <td><?= timeAgo($tag['created_at']) ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="tags.php?action=edit&id=<?= $tag['id'] ?>" 
                                                               class="btn btn-outline-primary" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="<?= SITE_URL ?>/tag.php?slug=<?= $tag['slug'] ?>" 
                                                               class="btn btn-outline-success" title="View" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <?php if ($tag['post_count'] == 0): ?>
                                                                <a href="tags.php?action=delete&id=<?= $tag['id'] ?>" 
                                                                   class="btn btn-outline-danger" title="Delete"
                                                                   onclick="return confirmDelete('Are you sure you want to delete this tag?')">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Add/Edit Tag Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><?= $action === 'add' ? 'Add New Tag' : 'Edit Tag' ?></h1>
                    <a href="tags.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Tags
                    </a>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= e($error) ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?= e($success) ?></div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Tag Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <?= getCSRFTokenInput() ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name_bn" class="form-label">Name (Bengali) *</label>
                                        <input type="text" class="form-control" id="name_bn" name="name_bn" 
                                               value="<?= e($tag_data['name_bn'] ?? '') ?>" required
                                               onkeyup="generateSlug(this.value, 'slug_preview')">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Name (English)</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?= e($tag_data['name'] ?? '') ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">URL Slug</label>
                                <div class="input-group">
                                    <span class="input-group-text"><?= SITE_URL ?>/tag/</span>
                                    <input type="text" class="form-control" id="slug_preview" readonly
                                           value="<?= e($tag_data['slug'] ?? '') ?>">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="Brief description of this tag"><?= e($tag_data['description'] ?? '') ?></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?= $action === 'add' ? 'Create Tag' : 'Update Tag' ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
$custom_scripts = "
<script>
    // Bulk selection functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.tag-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        toggleBulkDeleteButton();
    });
    
    document.querySelectorAll('.tag-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkDeleteButton);
    });
    
    function toggleBulkDeleteButton() {
        const checkedBoxes = document.querySelectorAll('.tag-checkbox:checked');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        
        if (checkedBoxes.length > 0) {
            bulkDeleteBtn.style.display = 'inline-block';
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }
    
    document.getElementById('bulkDeleteBtn').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.tag-checkbox:checked');
        if (checkedBoxes.length === 0) {
            alert('Please select tags to delete');
            return;
        }
        
        if (confirm('Are you sure you want to delete the selected tags?')) {
            document.getElementById('bulkForm').submit();
        }
    });
</script>
";

include 'includes/footer.php';
?>
