<?php
/**
 * Categories Management
 * Bengali News CMS
 */

require_once '../config/config.php';
requireLogin();

if (!hasPermission('editor')) {
    showMessage('You do not have permission to manage categories', 'error');
    redirect(ADMIN_URL . '/dashboard.php');
}

$action = $_GET['action'] ?? 'list';
$category_id = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token';
    } else {
        if ($action === 'add' || $action === 'edit') {
            $name = cleanInput($_POST['name']);
            $name_bn = cleanInput($_POST['name_bn']);
            $description = cleanInput($_POST['description']);
            $color = cleanInput($_POST['color']);
            $sort_order = (int)$_POST['sort_order'];
            $status = cleanInput($_POST['status']);
            
            // Generate slug
            $slug = generateSlug($name_bn ?: $name);
            
            // Handle icon upload
            $icon = '';
            if (isset($_FILES['icon']) && $_FILES['icon']['error'] === UPLOAD_ERR_OK) {
                $upload_result = validateFileUpload($_FILES['icon'], ALLOWED_IMAGE_TYPES);
                if ($upload_result['success']) {
                    $filename = generateSecureFilename($_FILES['icon']['name']);
                    $upload_path = UPLOAD_PATH . 'categories/' . $filename;
                    
                    if (move_uploaded_file($_FILES['icon']['tmp_name'], $upload_path)) {
                        $icon = 'categories/' . $filename;
                    }
                }
            }
            
            // Handle image upload
            $image = '';
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = validateFileUpload($_FILES['image'], ALLOWED_IMAGE_TYPES);
                if ($upload_result['success']) {
                    $filename = generateSecureFilename($_FILES['image']['name']);
                    $upload_path = UPLOAD_PATH . 'categories/' . $filename;
                    
                    if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        $image = 'categories/' . $filename;
                    }
                }
            }
            
            if ($action === 'add') {
                // Check if slug already exists
                $stmt = $db->prepare("SELECT id FROM categories WHERE slug = ?");
                $stmt->execute([$slug]);
                if ($stmt->fetch()) {
                    $slug .= '-' . time();
                }
                
                $sql = "INSERT INTO categories (name, name_bn, slug, description, icon, image, color, sort_order, status) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $stmt = $db->prepare($sql);
                $result = $stmt->execute([$name, $name_bn, $slug, $description, $icon, $image, $color, $sort_order, $status]);
                
                if ($result) {
                    $success = 'Category created successfully';
                    clearCacheByPattern('categories_');
                } else {
                    $error = 'Failed to create category';
                }
                
            } elseif ($action === 'edit' && $category_id) {
                // Get current category data
                $stmt = $db->prepare("SELECT * FROM categories WHERE id = ?");
                $stmt->execute([$category_id]);
                $current_category = $stmt->fetch();
                
                if (!$current_category) {
                    $error = 'Category not found';
                } else {
                    // Use existing files if no new ones uploaded
                    if (empty($icon)) {
                        $icon = $current_category['icon'];
                    }
                    if (empty($image)) {
                        $image = $current_category['image'];
                    }
                    
                    // Check if slug already exists (excluding current category)
                    $stmt = $db->prepare("SELECT id FROM categories WHERE slug = ? AND id != ?");
                    $stmt->execute([$slug, $category_id]);
                    if ($stmt->fetch()) {
                        $slug .= '-' . time();
                    }
                    
                    $sql = "UPDATE categories SET name = ?, name_bn = ?, slug = ?, description = ?, icon = ?, 
                                                 image = ?, color = ?, sort_order = ?, status = ?, updated_at = NOW()
                            WHERE id = ?";
                    
                    $stmt = $db->prepare($sql);
                    $result = $stmt->execute([$name, $name_bn, $slug, $description, $icon, $image, $color, $sort_order, $status, $category_id]);
                    
                    if ($result) {
                        $success = 'Category updated successfully';
                        clearCacheByPattern('categories_');
                    } else {
                        $error = 'Failed to update category';
                    }
                }
            }
        } elseif ($action === 'delete' && $category_id) {
            // Check if category has posts
            $stmt = $db->prepare("SELECT COUNT(*) as post_count FROM posts WHERE category_id = ?");
            $stmt->execute([$category_id]);
            $result = $stmt->fetch();
            
            if ($result['post_count'] > 0) {
                $error = 'Cannot delete category with existing posts';
            } else {
                $stmt = $db->prepare("DELETE FROM categories WHERE id = ?");
                if ($stmt->execute([$category_id])) {
                    $success = 'Category deleted successfully';
                    clearCacheByPattern('categories_');
                } else {
                    $error = 'Failed to delete category';
                }
            }
        }
    }
}

// Get category data for edit
$category_data = null;
if ($action === 'edit' && $category_id) {
    $stmt = $db->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$category_id]);
    $category_data = $stmt->fetch();
}

$page_title = ($action === 'add') ? 'Add Category' : (($action === 'edit') ? 'Edit Category' : 'Categories');
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($action === 'list'): ?>
        <!-- Categories List -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Categories</h1>
                    <a href="categories.php?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Category
                    </a>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <?php
                        // Get all categories
                        $stmt = $db->prepare("SELECT c.*, COUNT(p.id) as post_count 
                                             FROM categories c 
                                             LEFT JOIN posts p ON c.id = p.category_id 
                                             GROUP BY c.id 
                                             ORDER BY c.sort_order ASC, c.name_bn ASC");
                        $stmt->execute();
                        $categories = $stmt->fetchAll();
                        ?>
                        
                        <?php if (empty($categories)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <h5>No categories found</h5>
                                <p class="text-muted">Start by creating your first category</p>
                                <a href="categories.php?action=add" class="btn btn-primary">Add New Category</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Order</th>
                                            <th>Category</th>
                                            <th>Slug</th>
                                            <th>Posts</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($categories as $category): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-secondary"><?= $category['sort_order'] ?></span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($category['icon']): ?>
                                                            <img src="<?= UPLOAD_URL . $category['icon'] ?>" 
                                                                 class="me-2" width="24" height="24" alt="Icon">
                                                        <?php else: ?>
                                                            <div class="me-2" style="width: 24px; height: 24px; background-color: <?= e($category['color']) ?>; border-radius: 4px;"></div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <div class="fw-bold"><?= e($category['name_bn']) ?></div>
                                                            <small class="text-muted"><?= e($category['name']) ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <code><?= e($category['slug']) ?></code>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?= number_format($category['post_count']) ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $category['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                        <?= ucfirst($category['status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= timeAgo($category['created_at']) ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="categories.php?action=edit&id=<?= $category['id'] ?>" 
                                                           class="btn btn-outline-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="<?= SITE_URL ?>/category.php?slug=<?= $category['slug'] ?>" 
                                                           class="btn btn-outline-success" title="View" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($category['post_count'] == 0): ?>
                                                            <a href="categories.php?action=delete&id=<?= $category['id'] ?>" 
                                                               class="btn btn-outline-danger" title="Delete"
                                                               onclick="return confirmDelete('Are you sure you want to delete this category?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Add/Edit Category Form -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><?= $action === 'add' ? 'Add New Category' : 'Edit Category' ?></h1>
                    <a href="categories.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Categories
                    </a>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= e($error) ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?= e($success) ?></div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Category Information</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <?= getCSRFTokenInput() ?>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name_bn" class="form-label">Name (Bengali) *</label>
                                                <input type="text" class="form-control" id="name_bn" name="name_bn" 
                                                       value="<?= e($category_data['name_bn'] ?? '') ?>" required
                                                       onkeyup="generateSlug(this.value, 'slug_preview')">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">Name (English)</label>
                                                <input type="text" class="form-control" id="name" name="name" 
                                                       value="<?= e($category_data['name'] ?? '') ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">URL Slug</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?= SITE_URL ?>/category/</span>
                                            <input type="text" class="form-control" id="slug_preview" readonly
                                                   value="<?= e($category_data['slug'] ?? '') ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?= e($category_data['description'] ?? '') ?></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="color" class="form-label">Color</label>
                                                <input type="color" class="form-control form-control-color" id="color" name="color" 
                                                       value="<?= e($category_data['color'] ?? '#6c757d') ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="sort_order" class="form-label">Sort Order</label>
                                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                                       value="<?= e($category_data['sort_order'] ?? 0) ?>" min="0">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?= ($category_data['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= ($category_data['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            <?= $action === 'add' ? 'Create Category' : 'Update Category' ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- Category Icon -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Category Icon</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($category_data['icon'])): ?>
                                    <div class="mb-3 text-center">
                                        <img src="<?= UPLOAD_URL . $category_data['icon'] ?>" 
                                             class="img-fluid" style="max-width: 64px; max-height: 64px;" alt="Current icon">
                                    </div>
                                <?php endif; ?>
                                
                                <input type="file" class="form-control" name="icon" accept="image/*"
                                       onchange="previewImage(this, 'icon_preview')">
                                <div class="form-text">Small icon for category (recommended: 32x32px)</div>
                                
                                <img id="icon_preview" class="img-fluid mt-2" style="display: none; max-width: 64px;" alt="Preview">
                            </div>
                        </div>
                        
                        <!-- Category Image -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Category Image</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($category_data['image'])): ?>
                                    <div class="mb-3">
                                        <img src="<?= UPLOAD_URL . $category_data['image'] ?>" 
                                             class="img-fluid rounded" alt="Current image">
                                    </div>
                                <?php endif; ?>
                                
                                <input type="file" class="form-control" name="image" accept="image/*"
                                       onchange="previewImage(this, 'image_preview')">
                                <div class="form-text">Featured image for category page</div>
                                
                                <img id="image_preview" class="img-fluid rounded mt-2" style="display: none;" alt="Preview">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
