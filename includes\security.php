<?php
/**
 * Security Functions
 * Bengali News CMS
 */

/**
 * Generate CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Get CSRF Token HTML Input
 */
function getCSRFTokenInput() {
    return '<input type="hidden" name="csrf_token" value="' . generateCSRFToken() . '">';
}

/**
 * Sanitize input to prevent XSS
 */
function sanitizeHTML($input) {
    // Allow basic HTML tags for content
    $allowed_tags = '<p><br><strong><b><em><i><u><h1><h2><h3><h4><h5><h6><ul><ol><li><a><img><blockquote><code><pre>';
    return strip_tags($input, $allowed_tags);
}

/**
 * Clean input for database
 */
function cleanInput($input) {
    if (is_array($input)) {
        return array_map('cleanInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Validate password strength
 */
function validatePassword($password) {
    // At least 8 characters, one uppercase, one lowercase, one number
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

/**
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, HASH_ALGO);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate secure random string
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Rate limiting for login attempts
 */
function checkLoginAttempts($ip_address) {
    global $db;
    
    // Clean old attempts (older than lockout time)
    $cleanup_time = date('Y-m-d H:i:s', time() - LOGIN_LOCKOUT_TIME);
    $stmt = $db->prepare("DELETE FROM login_attempts WHERE attempted_at < ?");
    $stmt->execute([$cleanup_time]);
    
    // Count recent attempts
    $stmt = $db->prepare("SELECT COUNT(*) as attempts FROM login_attempts WHERE ip_address = ? AND attempted_at > ?");
    $stmt->execute([$ip_address, $cleanup_time]);
    $result = $stmt->fetch();
    
    return $result['attempts'] < MAX_LOGIN_ATTEMPTS;
}

/**
 * Record login attempt
 */
function recordLoginAttempt($ip_address, $username = null, $success = false) {
    global $db;
    
    // Create login_attempts table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ip_address VARCHAR(45) NOT NULL,
        username VARCHAR(50) DEFAULT NULL,
        success BOOLEAN DEFAULT FALSE,
        attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_ip_time (ip_address, attempted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    
    $stmt = $db->prepare("INSERT INTO login_attempts (ip_address, username, success) VALUES (?, ?, ?)");
    $stmt->execute([$ip_address, $username, $success]);
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowed_types = null) {
    if (!isset($file['error']) || is_array($file['error'])) {
        return ['success' => false, 'message' => 'Invalid file upload'];
    }
    
    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            return ['success' => false, 'message' => 'No file uploaded'];
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            return ['success' => false, 'message' => 'File too large'];
        default:
            return ['success' => false, 'message' => 'Upload error'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File size exceeds limit'];
    }
    
    $file_info = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($file_info, $file['tmp_name']);
    finfo_close($file_info);
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if ($allowed_types === null) {
        $allowed_types = array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_FILE_TYPES);
    }
    
    if (!in_array($extension, $allowed_types)) {
        return ['success' => false, 'message' => 'File type not allowed'];
    }
    
    // Additional security checks for images
    if (in_array($extension, ALLOWED_IMAGE_TYPES)) {
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            return ['success' => false, 'message' => 'Invalid image file'];
        }
    }
    
    return ['success' => true, 'mime_type' => $mime_type, 'extension' => $extension];
}

/**
 * Generate secure filename
 */
function generateSecureFilename($original_name) {
    $extension = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
    $filename = generateRandomString(16) . '_' . time() . '.' . $extension;
    return $filename;
}

/**
 * Prevent directory traversal
 */
function sanitizePath($path) {
    // Remove any path traversal attempts
    $path = str_replace(['../', '..\\', '../', '..\\'], '', $path);
    return $path;
}

/**
 * Check if user has permission for specific action
 */
function checkPermission($action, $resource = null) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['user_role'];
    
    // Admin has all permissions
    if ($user_role === 'admin') {
        return true;
    }
    
    // Define permissions
    $permissions = [
        'writer' => [
            'create_post', 'edit_own_post', 'view_own_post', 'upload_media'
        ],
        'editor' => [
            'create_post', 'edit_post', 'delete_post', 'publish_post', 
            'manage_categories', 'manage_tags', 'upload_media', 'manage_media'
        ],
        'admin' => ['*'] // All permissions
    ];
    
    if (!isset($permissions[$user_role])) {
        return false;
    }
    
    return in_array($action, $permissions[$user_role]) || in_array('*', $permissions[$user_role]);
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = null) {
    global $db;
    
    // Create security_logs table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS security_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event VARCHAR(100) NOT NULL,
        user_id INT DEFAULT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT DEFAULT NULL,
        details TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_event (event),
        INDEX idx_user (user_id),
        INDEX idx_ip (ip_address)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    
    $user_id = $_SESSION['user_id'] ?? null;
    $ip_address = getClientIP();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    $stmt = $db->prepare("INSERT INTO security_logs (event, user_id, ip_address, user_agent, details) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([$event, $user_id, $ip_address, $user_agent, $details]);
}

/**
 * Escape output for HTML
 */
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Validate and sanitize URL
 */
function sanitizeURL($url) {
    return filter_var($url, FILTER_SANITIZE_URL);
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}
?>
