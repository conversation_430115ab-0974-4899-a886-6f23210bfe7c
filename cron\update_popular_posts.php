<?php
/**
 * Cron Job: Update Popular Posts Rankings
 * Bengali News CMS
 * 
 * Run this script every hour to update popular posts rankings
 * Add to crontab: 0 * * * * /usr/bin/php /path/to/your/site/cron/update_popular_posts.php
 */

// Prevent direct web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

require_once dirname(__DIR__) . '/config/config.php';

echo "Starting popular posts ranking update...\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";

try {
    // Update popular posts ranking
    $success = updatePopularPostsRanking();
    
    if ($success) {
        echo "✓ Popular posts ranking updated successfully\n";
        
        // Clean up old view records (older than 30 days)
        $cleanup_date = date('Y-m-d H:i:s', strtotime('-30 days'));
        $stmt = $db->prepare("DELETE FROM post_views WHERE viewed_at < ?");
        $stmt->execute([$cleanup_date]);
        $deleted_views = $stmt->rowCount();
        
        echo "✓ Cleaned up $deleted_views old view records\n";
        
        // Clean up old rankings (older than 30 days)
        $cleanup_date = date('Y-m-d', strtotime('-30 days'));
        $stmt = $db->prepare("DELETE FROM popular_posts_ranking WHERE period_end < ?");
        $stmt->execute([$cleanup_date]);
        $deleted_rankings = $stmt->rowCount();
        
        echo "✓ Cleaned up $deleted_rankings old ranking records\n";
        
        // Clear all related caches
        clearCacheByPattern('popular_posts_');
        clearCacheByPattern('trending_posts_');
        clearCacheByPattern('ranked_popular_posts_');
        clearCacheByPattern('latest_posts_');
        
        echo "✓ Cache cleared\n";
        
        // Log the update
        logSecurityEvent('cron_popular_posts_update', 'Popular posts ranking updated successfully');
        
    } else {
        echo "✗ Failed to update popular posts ranking\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    error_log('Popular posts cron job error: ' . $e->getMessage());
    exit(1);
}

echo "Popular posts ranking update completed successfully\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";
echo "---\n";
?>
