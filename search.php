<?php
/**
 * Search Page - Bengali News CMS
 */

require_once 'config/config.php';

$query = trim($_GET['q'] ?? '');
$page = (int)($_GET['page'] ?? 1);
$posts_per_page = 10;

$search_results = [];
$total_results = 0;
$total_pages = 0;

if (!empty($query)) {
    // Get search results
    $search_results = searchPosts($query, $page, $posts_per_page);
    $total_results = getSearchResultsCount($query);
    $total_pages = ceil($total_results / $posts_per_page);
}

// Set page meta data
$page_title = !empty($query) ? "'{$query}' এর জন্য অনুসন্ধান ফলাফল" : 'অনুসন্ধান';
$page_description = !empty($query) ? "'{$query}' সম্পর্কিত সংবাদ খুঁজুন" : 'সংবাদ অনুসন্ধান করুন';

include 'includes/frontend_header.php';
?>

<div class="container my-4">
    <!-- Search Header -->
    <div class="search-header mb-4">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h1 class="text-center mb-4">
                    <i class="fas fa-search text-primary me-2"></i>সংবাদ অনুসন্ধান
                </h1>
                
                <!-- Search Form -->
                <form action="search.php" method="GET" class="search-form">
                    <div class="input-group input-group-lg">
                        <input type="text" name="q" class="form-control" 
                               placeholder="সংবাদ, বিষয় বা লেখকের নাম লিখুন..." 
                               value="<?= e($query) ?>" required>
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search me-2"></i>খুঁজুন
                        </button>
                    </div>
                </form>
                
                <?php if (!empty($query)): ?>
                    <div class="search-info mt-3 text-center">
                        <p class="text-muted">
                            "<strong><?= e($query) ?></strong>" এর জন্য <strong><?= number_format($total_results) ?></strong> টি ফলাফল পাওয়া গেছে
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Search Results -->
        <div class="col-lg-8">
            <?php if (empty($query)): ?>
                <!-- Search Tips -->
                <div class="search-tips">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-lightbulb text-warning me-2"></i>অনুসন্ধানের টিপস
                            </h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    সঠিক বানান ব্যবহার করুন
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    সংক্ষিপ্ত এবং স্পষ্ট শব্দ ব্যবহার করুন
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    একাধিক শব্দ ব্যবহার করে আরো নির্দিষ্ট ফলাফল পান
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    ইংরেজি এবং বাংলা উভয় ভাষায় অনুসন্ধান করতে পারেন
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
            <?php elseif (empty($search_results)): ?>
                <!-- No Results -->
                <div class="no-results text-center py-5">
                    <i class="fas fa-search fa-5x text-muted mb-4"></i>
                    <h3>কোন ফলাফল পাওয়া যায়নি</h3>
                    <p class="text-muted mb-4">
                        "<strong><?= e($query) ?></strong>" এর জন্য কোন সংবাদ পাওয়া যায়নি।
                    </p>
                    
                    <div class="suggestions">
                        <h5>পরামর্শ:</h5>
                        <ul class="list-unstyled text-muted">
                            <li>• বানান পরীক্ষা করুন</li>
                            <li>• আরো সাধারণ শব্দ ব্যবহার করুন</li>
                            <li>• কম শব্দ ব্যবহার করুন</li>
                            <li>• সমার্থক শব্দ ব্যবহার করে দেখুন</li>
                        </ul>
                    </div>
                    
                    <a href="<?= SITE_URL ?>" class="btn btn-primary mt-3">
                        <i class="fas fa-home me-2"></i>হোমে ফিরে যান
                    </a>
                </div>
                
            <?php else: ?>
                <!-- Search Results -->
                <div class="search-results">
                    <?php foreach ($search_results as $post): ?>
                        <article class="search-result-item mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <?php if ($post['featured_image']): ?>
                                            <div class="col-md-3">
                                                <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                                                     class="img-fluid rounded" alt="<?= e($post['title_bn']) ?>">
                                            </div>
                                            <div class="col-md-9">
                                        <?php else: ?>
                                            <div class="col-12">
                                        <?php endif; ?>
                                            
                                            <!-- Category Badge -->
                                            <span class="badge category-badge mb-2" 
                                                  style="background-color: <?= e($post['category_color']) ?>">
                                                <?= e($post['category_name']) ?>
                                            </span>
                                            
                                            <!-- Post Title -->
                                            <h5 class="card-title">
                                                <a href="post.php?slug=<?= $post['slug'] ?>" 
                                                   class="text-decoration-none">
                                                    <?= e($post['title_bn']) ?>
                                                </a>
                                            </h5>
                                            
                                            <!-- Post Excerpt -->
                                            <?php if ($post['excerpt']): ?>
                                                <p class="card-text text-muted">
                                                    <?= e(truncateText($post['excerpt'], 150)) ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <!-- Post Meta -->
                                            <div class="post-meta">
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i><?= e($post['author_name']) ?>
                                                    <i class="fas fa-clock ms-3 me-1"></i><?= timeAgo($post['published_at']) ?>
                                                    <i class="fas fa-eye ms-3 me-1"></i><?= number_format($post['view_count']) ?> ভিউ
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Search results pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <!-- Previous Page -->
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="search.php?q=<?= urlencode($query) ?>&page=<?= $page - 1 ?>">
                                        <i class="fas fa-chevron-left me-1"></i>পূর্ববর্তী
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);
                            
                            for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="search.php?q=<?= urlencode($query) ?>&page=<?= $i ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <!-- Next Page -->
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="search.php?q=<?= urlencode($query) ?>&page=<?= $page + 1 ?>">
                                        পরবর্তী<i class="fas fa-chevron-right ms-1"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Popular Searches -->
            <div class="sidebar-widget mb-4">
                <h4 class="widget-title">
                    <i class="fas fa-chart-line text-info me-2"></i>জনপ্রিয় অনুসন্ধান
                </h4>
                <div class="popular-searches">
                    <?php
                    // You can implement popular search tracking here
                    $popular_searches = ['রাজনীতি', 'অর্থনীতি', 'খেলাধুলা', 'প্রযুক্তি', 'বিনোদন'];
                    foreach ($popular_searches as $search_term):
                    ?>
                        <a href="search.php?q=<?= urlencode($search_term) ?>" 
                           class="badge bg-light text-dark text-decoration-none me-2 mb-2">
                            <?= e($search_term) ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Categories -->
            <div class="sidebar-widget mb-4">
                <h4 class="widget-title">
                    <i class="fas fa-tags text-primary me-2"></i>বিভাগসমূহ
                </h4>
                <div class="categories-list">
                    <?php 
                    $categories = getCategories();
                    foreach ($categories as $category): 
                    ?>
                        <a href="category.php?slug=<?= $category['slug'] ?>" 
                           class="category-item d-flex align-items-center justify-content-between text-decoration-none mb-2 p-3 rounded">
                            <div class="d-flex align-items-center">
                                <?php if ($category['icon']): ?>
                                    <img src="<?= UPLOAD_URL . $category['icon'] ?>" 
                                         width="24" height="24" class="me-2" alt="<?= e($category['name_bn']) ?>">
                                <?php else: ?>
                                    <div class="category-color-box me-2" 
                                         style="width: 24px; height: 24px; background-color: <?= e($category['color']) ?>; border-radius: 4px;"></div>
                                <?php endif; ?>
                                <span class="fw-medium"><?= e($category['name_bn']) ?></span>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Latest Posts -->
            <?php 
            $latest_posts = getLatestPosts(5);
            if (!empty($latest_posts)): 
            ?>
                <div class="sidebar-widget mb-4">
                    <h4 class="widget-title">
                        <i class="fas fa-clock text-success me-2"></i>সর্বশেষ সংবাদ
                    </h4>
                    <div class="latest-posts">
                        <?php foreach ($latest_posts as $latest): ?>
                            <div class="latest-post-item mb-3">
                                <div class="d-flex">
                                    <?php if ($latest['featured_image']): ?>
                                        <div class="latest-image me-3">
                                            <img src="<?= UPLOAD_URL . $latest['featured_image'] ?>" 
                                                 class="rounded" width="80" height="60" 
                                                 style="object-fit: cover;" alt="<?= e($latest['title_bn']) ?>">
                                        </div>
                                    <?php endif; ?>
                                    <div class="latest-content flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="post.php?slug=<?= $latest['slug'] ?>" 
                                               class="text-decoration-none">
                                                <?= e(truncateText($latest['title_bn'], 60)) ?>
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i><?= timeAgo($latest['published_at']) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/frontend_footer.php'; ?>
