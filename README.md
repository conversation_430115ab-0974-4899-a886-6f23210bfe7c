# Bengali News CMS

A modern, feature-rich Content Management System specifically designed for Bengali news websites. Built with PHP, MySQL, and Bootstrap 5.

## Features

### Core Features
- **Multi-language Support**: Bengali and English content management
- **Responsive Design**: Mobile-first, responsive layout
- **SEO Optimized**: Built-in SEO features with meta tags and clean URLs
- **User Management**: Role-based access control (<PERSON><PERSON>, Editor, Author)
- **Category Management**: Organize content with categories and tags
- **Media Library**: Upload and manage images and documents
- **Page Management**: Create and manage static pages
- **Search Functionality**: Full-text search across all content

### Advanced Features
- **Breaking News Ticker**: Highlight urgent news
- **Featured Posts**: Showcase important articles
- **Popular Posts Tracking**: Automatic popularity ranking based on views
- **Trending Posts**: Real-time trending content based on recent activity
- **Newsletter System**: Email subscription management
- **Security Features**: CSRF protection, rate limiting, security logging
- **Caching System**: Database-based caching for improved performance
- **WordPress Import**: Import content from WordPress sites

### Technical Features
- **Clean URLs**: SEO-friendly URL structure
- **Auto-save**: Automatic saving of draft content
- **Image Optimization**: Secure file upload with validation
- **Database Optimization**: Efficient queries with proper indexing
- **Error Handling**: Comprehensive error logging and handling

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher (or MariaDB 10.2+)
- Apache/Nginx web server
- mod_rewrite enabled (for clean URLs)
- GD extension (for image processing)
- PDO MySQL extension
- mbstring extension

## Installation

### 1. Download and Extract
```bash
# Download the CMS files
git clone https://github.com/your-repo/bengali-news-cms.git
cd bengali-news-cms
```

### 2. Database Setup
1. Create a MySQL database
2. Import the database schema:
```sql
-- The database tables will be created automatically during installation
```

### 3. Configuration
1. Copy `config/config.example.php` to `config/config.php`
2. Edit `config/config.php` with your database credentials:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('SITE_URL', 'https://your-domain.com');
```

### 4. File Permissions
Set proper permissions for upload directories:
```bash
chmod 755 uploads/
chmod 755 uploads/images/
chmod 755 uploads/documents/
chmod 755 uploads/categories/
chmod 755 cache/
```

### 5. Run Installation
1. Navigate to `http://your-domain.com/install.php`
2. Follow the installation wizard
3. Create your admin account
4. Complete the setup

### 6. Install Default Pages (Optional)
```bash
php install_default_pages.php
```

### 7. Setup Cron Jobs (Recommended)
Add these cron jobs for optimal performance:
```bash
# Update popular posts ranking every hour
0 * * * * /usr/bin/php /path/to/your/site/cron/update_popular_posts.php

# Clean up cache daily at 2 AM
0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_cache.php
```

## Configuration

### Basic Settings
Access admin panel at `/admin/` and configure:
- Site title and description
- Contact information
- Social media links
- SEO settings

### Security Settings
- Change default admin credentials
- Configure rate limiting
- Set up SSL certificate
- Enable security logging

### Performance Optimization
- Enable caching
- Optimize images
- Configure CDN (if available)
- Set up database optimization

## Usage

### Admin Panel
Access the admin panel at `/admin/login.php` with your admin credentials.

#### Dashboard
- Overview of site statistics
- Recent posts and popular content
- Quick actions for common tasks

#### Content Management
- **Posts**: Create, edit, and manage news articles
- **Categories**: Organize content by topics
- **Tags**: Add tags for better content discovery
- **Pages**: Manage static pages like About Us, Contact

#### Media Management
- Upload images and documents
- Organize media files
- Bulk operations for media management

#### User Management
- Add/edit users with different roles
- Manage user permissions
- Monitor user activity

### Frontend Features
- Responsive design for all devices
- Fast loading with optimized code
- SEO-friendly URLs
- Social media sharing
- Newsletter subscription
- Advanced search functionality

## File Structure

```
bengali-news-cms/
├── admin/                  # Admin panel files
│   ├── includes/          # Admin includes
│   ├── ajax/              # AJAX handlers
│   └── *.php              # Admin pages
├── config/                # Configuration files
├── includes/              # Core includes
├── uploads/               # Upload directories
├── cache/                 # Cache directory
├── cron/                  # Cron job scripts
├── ajax/                  # Frontend AJAX handlers
├── *.php                  # Frontend pages
└── README.md              # This file
```

## API Endpoints

### AJAX Endpoints
- `/ajax/load_more_posts.php` - Load more posts
- `/ajax/newsletter_subscribe.php` - Newsletter subscription
- `/ajax/update_view_count.php` - Update post view count
- `/ajax/get_trending_posts.php` - Get trending posts

### Admin AJAX Endpoints
- `/admin/ajax/upload.php` - File upload
- `/admin/ajax/auto_save.php` - Auto-save content
- `/admin/ajax/get_media.php` - Get media details

## Security Features

### Built-in Security
- CSRF token protection
- SQL injection prevention
- XSS protection
- Rate limiting for login attempts
- Secure file upload validation
- Security event logging

### Recommended Security Measures
- Use HTTPS
- Regular backups
- Keep PHP and MySQL updated
- Use strong passwords
- Monitor security logs

## Performance Optimization

### Caching
- Database query caching
- Page fragment caching
- Automatic cache invalidation

### Database Optimization
- Proper indexing
- Query optimization
- Regular cleanup of old data

### Frontend Optimization
- Minified CSS/JS
- Image optimization
- Lazy loading
- CDN support

## Troubleshooting

### Common Issues

#### Installation Problems
- Check file permissions
- Verify database credentials
- Ensure PHP extensions are installed

#### Performance Issues
- Enable caching
- Optimize database
- Check server resources

#### Security Concerns
- Review security logs
- Update passwords
- Check file permissions

### Debug Mode
Enable debug mode in `config/config.php`:
```php
define('DEBUG_MODE', true);
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: https://docs.bengalinews-cms.com
- Issues: https://github.com/your-repo/bengali-news-cms/issues

## Changelog

### Version 1.0.0
- Initial release
- Core CMS functionality
- Admin panel
- Frontend display
- Security features
- Performance optimization

---

**Bengali News CMS** - Built with ❤️ for the Bengali community
