<?php
/**
 * 404 Error Page - Bengali News CMS
 */

require_once 'config/config.php';

// Set page meta data
$page_title = 'পৃষ্ঠা পাওয়া যায়নি - 404 Error';
$page_description = 'দুঃখিত, আপনি যে পৃষ্ঠাটি খুঁজছেন তা পাওয়া যায়নি।';

include 'includes/frontend_header.php';
?>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <!-- 404 Error -->
            <div class="error-404 mb-5">
                <h1 class="display-1 text-primary fw-bold">404</h1>
                <h2 class="mb-4">পৃষ্ঠা পাওয়া যায়নি</h2>
                <p class="lead text-muted mb-4">
                    দুঃখিত, আপনি যে পৃষ্ঠাটি খুঁজছেন তা আর উপলব্ধ নেই বা সরানো হয়েছে।
                </p>
                
                <!-- Search Box -->
                <div class="search-section mb-5">
                    <h5 class="mb-3">আপনি কি খুঁজছেন?</h5>
                    <form action="search.php" method="GET" class="search-form">
                        <div class="input-group input-group-lg justify-content-center">
                            <div style="max-width: 400px; width: 100%;">
                                <div class="input-group">
                                    <input type="text" name="q" class="form-control" 
                                           placeholder="সংবাদ খুঁজুন..." required>
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions mb-5">
                    <h5 class="mb-3">অথবা এখানে যান:</h5>
                    <div class="d-flex flex-wrap justify-content-center gap-3">
                        <a href="<?= SITE_URL ?>" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>হোম পেজ
                        </a>
                        <a href="javascript:history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>পূর্ববর্তী পৃষ্ঠা
                        </a>
                        <a href="contact.php" class="btn btn-outline-info">
                            <i class="fas fa-envelope me-2"></i>যোগাযোগ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Popular Content -->
    <div class="row">
        <div class="col-12">
            <h3 class="text-center mb-4">জনপ্রিয় সংবাদ</h3>
        </div>
        
        <?php 
        $popular_posts = getPopularPosts(6);
        if (!empty($popular_posts)): 
        ?>
            <?php foreach ($popular_posts as $post): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card news-card h-100">
                        <?php if ($post['featured_image']): ?>
                            <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                                 class="card-img-top" alt="<?= e($post['title_bn']) ?>">
                        <?php endif; ?>
                        <div class="card-body">
                            <span class="badge category-badge mb-2" 
                                  style="background-color: <?= e($post['category_color']) ?>">
                                <?= e($post['category_name']) ?>
                            </span>
                            <h6 class="card-title">
                                <a href="post.php?slug=<?= $post['slug'] ?>" 
                                   class="text-decoration-none">
                                    <?= e(truncateText($post['title_bn'], 80)) ?>
                                </a>
                            </h6>
                            <small class="text-muted">
                                <i class="fas fa-eye me-1"></i><?= number_format($post['view_count']) ?> ভিউ
                                <i class="fas fa-clock ms-2 me-1"></i><?= timeAgo($post['published_at']) ?>
                            </small>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12 text-center">
                <p class="text-muted">কোন জনপ্রিয় সংবাদ পাওয়া যায়নি।</p>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Categories -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">বিভাগসমূহ</h3>
        </div>
        
        <?php 
        $categories = getCategories();
        foreach ($categories as $category): 
        ?>
            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                <a href="category.php?slug=<?= $category['slug'] ?>" 
                   class="category-card card text-decoration-none h-100">
                    <div class="card-body text-center">
                        <?php if ($category['icon']): ?>
                            <img src="<?= UPLOAD_URL . $category['icon'] ?>" 
                                 width="48" height="48" class="mb-3" alt="<?= e($category['name_bn']) ?>">
                        <?php else: ?>
                            <div class="category-color-box mx-auto mb-3" 
                                 style="width: 48px; height: 48px; background-color: <?= e($category['color']) ?>; border-radius: 8px;"></div>
                        <?php endif; ?>
                        <h6 class="card-title"><?= e($category['name_bn']) ?></h6>
                        <?php if ($category['description']): ?>
                            <small class="text-muted"><?= e(truncateText($category['description'], 50)) ?></small>
                        <?php endif; ?>
                    </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<style>
.error-404 h1 {
    font-size: 8rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.category-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
    color: inherit;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    color: inherit;
    text-decoration: none;
}

@media (max-width: 768px) {
    .error-404 h1 {
        font-size: 5rem;
    }
    
    .quick-actions .btn {
        margin-bottom: 0.5rem;
    }
}
</style>

<?php include 'includes/frontend_footer.php'; ?>
