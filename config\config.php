<?php
/**
 * Main Configuration File
 * Bengali News CMS
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Dhaka');

// Site Configuration
define('SITE_URL', 'http://localhost/social-media');
define('ADMIN_URL', SITE_URL . '/admin');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('UPLOAD_URL', SITE_URL . '/uploads/');

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'bengali_news_cms');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Security Configuration
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600); // 1 hour
define('CSRF_TOKEN_NAME', 'csrf_token');
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File Upload Configuration
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'txt']);

// Pagination Configuration
define('POSTS_PER_PAGE', 10);
define('ADMIN_POSTS_PER_PAGE', 20);

// Cache Configuration
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // 1 hour

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/security.php';

// Initialize database connection
$database = new Database();
$db = $database->connect();

// Auto-create tables if they don't exist
if ($db) {
    $database->createTables();
    $database->insertDefaultData();
}

/**
 * Autoloader for classes
 */
spl_autoload_register(function ($class_name) {
    $directories = [
        __DIR__ . '/../classes/',
        __DIR__ . '/../admin/classes/',
        __DIR__ . '/../includes/classes/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

/**
 * Global helper functions
 */
function getDB() {
    global $db;
    return $db;
}

function getSetting($key, $default = null) {
    global $db;
    $stmt = $db->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

function setSetting($key, $value, $type = 'text', $description = null) {
    global $db;
    $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                         VALUES (?, ?, ?, ?) 
                         ON DUPLICATE KEY UPDATE setting_value = ?, setting_type = ?, description = ?");
    return $stmt->execute([$key, $value, $type, $description, $value, $type, $description]);
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . ADMIN_URL . '/login.php');
        exit;
    }
}

function hasPermission($required_role) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['user_role'];
    $roles = ['writer' => 1, 'editor' => 2, 'admin' => 3];
    
    return $roles[$user_role] >= $roles[$required_role];
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $db;
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

function generateSlug($text) {
    // Convert Bengali and English text to URL-friendly slug
    $text = trim($text);
    $text = strtolower($text);
    
    // Replace Bengali characters with English equivalents (basic mapping)
    $bengali_to_english = [
        'আ' => 'a', 'ই' => 'i', 'ঈ' => 'i', 'উ' => 'u', 'ঊ' => 'u', 'ঋ' => 'ri',
        'এ' => 'e', 'ঐ' => 'oi', 'ও' => 'o', 'ঔ' => 'ou',
        'ক' => 'k', 'খ' => 'kh', 'গ' => 'g', 'ঘ' => 'gh', 'ঙ' => 'ng',
        'চ' => 'ch', 'ছ' => 'chh', 'জ' => 'j', 'ঝ' => 'jh', 'ঞ' => 'ny',
        'ট' => 't', 'ঠ' => 'th', 'ড' => 'd', 'ঢ' => 'dh', 'ণ' => 'n',
        'ত' => 't', 'থ' => 'th', 'দ' => 'd', 'ধ' => 'dh', 'ন' => 'n',
        'প' => 'p', 'ফ' => 'ph', 'ব' => 'b', 'ভ' => 'bh', 'ম' => 'm',
        'য' => 'y', 'র' => 'r', 'ল' => 'l', 'শ' => 'sh', 'ষ' => 'sh',
        'স' => 's', 'হ' => 'h', 'য়' => 'y', 'ড়' => 'r', 'ঢ়' => 'rh',
        'ৎ' => 't', 'ং' => 'ng', 'ঃ' => 'h', 'ঁ' => 'n'
    ];
    
    $text = strtr($text, $bengali_to_english);
    
    // Remove special characters and replace spaces with hyphens
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    $text = trim($text, '-');
    
    return $text;
}

function formatDate($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'এখনই';
    if ($time < 3600) return floor($time/60) . ' মিনিট আগে';
    if ($time < 86400) return floor($time/3600) . ' ঘন্টা আগে';
    if ($time < 2592000) return floor($time/86400) . ' দিন আগে';
    if ($time < 31536000) return floor($time/2592000) . ' মাস আগে';
    
    return floor($time/31536000) . ' বছর আগে';
}

function truncateText($text, $length = 150, $suffix = '...') {
    if (mb_strlen($text, 'UTF-8') <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length, 'UTF-8') . $suffix;
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function redirect($url) {
    header("Location: $url");
    exit;
}

function showMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

// Create upload directories if they don't exist
$upload_dirs = [
    UPLOAD_PATH,
    UPLOAD_PATH . 'images/',
    UPLOAD_PATH . 'documents/',
    UPLOAD_PATH . 'thumbnails/',
    UPLOAD_PATH . 'categories/',
    UPLOAD_PATH . 'profiles/'
];

foreach ($upload_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Set default charset
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
?>
