#!/bin/bash

# Bengali News CMS - Quick Start Script
# This script helps you get the CMS running quickly

echo "🚀 Bengali News CMS - Quick Start"
echo "=================================="

# Check if Docker is installed
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "✅ Docker found! Starting with Docker..."
    
    # Create necessary directories
    mkdir -p uploads/images uploads/documents uploads/categories cache config
    chmod -R 775 uploads cache config
    
    # Start Docker containers
    echo "🐳 Starting Docker containers..."
    docker-compose up -d
    
    echo ""
    echo "🎉 Server is starting up!"
    echo "📱 Your CMS will be available at: http://localhost:8080"
    echo "🗄️  phpMyAdmin will be available at: http://localhost:8081"
    echo ""
    echo "Database credentials:"
    echo "  Host: localhost (or db from within containers)"
    echo "  Database: bengali_news"
    echo "  Username: root"
    echo "  Password: password"
    echo ""
    echo "⏳ Please wait 30-60 seconds for services to fully start..."
    echo "🔧 Then visit http://localhost:8080/install.php to complete setup"
    
elif command -v php &> /dev/null; then
    echo "✅ PHP found! Starting with built-in server..."
    
    # Check PHP version
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    echo "📋 PHP Version: $PHP_VERSION"
    
    # Create necessary directories
    mkdir -p uploads/images uploads/documents uploads/categories cache config
    chmod -R 775 uploads cache config
    
    # Start PHP built-in server
    echo "🚀 Starting PHP development server..."
    echo "📱 Your CMS will be available at: http://localhost:8000"
    echo ""
    echo "⚠️  Note: You'll need to set up MySQL separately"
    echo "🔧 Visit http://localhost:8000/install.php to complete setup"
    echo ""
    echo "Press Ctrl+C to stop the server"
    echo ""
    
    php -S localhost:8000
    
else
    echo "❌ Neither Docker nor PHP found!"
    echo ""
    echo "Please install one of the following:"
    echo ""
    echo "🐳 Docker (Recommended):"
    echo "   - Windows/Mac: Download Docker Desktop"
    echo "   - Linux: Install docker and docker-compose"
    echo ""
    echo "🐘 PHP + MySQL:"
    echo "   - XAMPP: https://www.apachefriends.org/"
    echo "   - WAMP: http://www.wampserver.com/ (Windows)"
    echo "   - MAMP: https://www.mamp.info/ (Mac)"
    echo ""
    echo "📚 See README.md for detailed installation instructions"
fi
