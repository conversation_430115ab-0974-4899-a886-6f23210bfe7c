<?php
/**
 * Category Page - Bengali News CMS
 */

require_once 'config/config.php';

$slug = $_GET['slug'] ?? '';
$page = (int)($_GET['page'] ?? 1);

if (empty($slug)) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Get category by slug
$category = getCategoryBySlug($slug);

if (!$category) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Get posts by category
$posts_per_page = 12;
$posts = getPostsByCategory($slug, $page, $posts_per_page);

// Get total posts count for pagination
$stmt = $db->prepare("SELECT COUNT(*) as total FROM posts p 
                     JOIN categories c ON p.category_id = c.id 
                     WHERE p.status = 'published' AND c.slug = ?");
$stmt->execute([$slug]);
$total_posts = $stmt->fetch()['total'];
$total_pages = ceil($total_posts / $posts_per_page);

// Set page meta data
$page_title = $category['name_bn'] . ' - ' . getSetting('site_title');
$page_description = $category['description'] ?: $category['name_bn'] . ' বিভাগের সর্বশেষ সংবাদ';

include 'includes/frontend_header.php';
?>

<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= SITE_URL ?>">হোম</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= e($category['name_bn']) ?></li>
        </ol>
    </nav>
    
    <!-- Category Header -->
    <div class="category-header mb-5">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center mb-3">
                    <?php if ($category['icon']): ?>
                        <img src="<?= UPLOAD_URL . $category['icon'] ?>" 
                             width="48" height="48" class="me-3" alt="<?= e($category['name_bn']) ?>">
                    <?php else: ?>
                        <div class="category-color-box me-3" 
                             style="width: 48px; height: 48px; background-color: <?= e($category['color']) ?>; border-radius: 8px;"></div>
                    <?php endif; ?>
                    <div>
                        <h1 class="mb-0"><?= e($category['name_bn']) ?></h1>
                        <?php if ($category['name']): ?>
                            <p class="text-muted mb-0"><?= e($category['name']) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if ($category['description']): ?>
                    <p class="lead text-muted"><?= e($category['description']) ?></p>
                <?php endif; ?>
                
                <div class="category-stats">
                    <span class="badge bg-primary me-2">
                        <i class="fas fa-newspaper me-1"></i><?= number_format($total_posts) ?> সংবাদ
                    </span>
                    <span class="text-muted">
                        পৃষ্ঠা <?= $page ?> এর <?= $total_pages ?>
                    </span>
                </div>
            </div>
            
            <?php if ($category['image']): ?>
                <div class="col-md-4 text-end">
                    <img src="<?= UPLOAD_URL . $category['image'] ?>" 
                         class="img-fluid rounded" alt="<?= e($category['name_bn']) ?>" 
                         style="max-height: 200px; object-fit: cover;">
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <?php if (empty($posts)): ?>
                <!-- No Posts Found -->
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                    <h3>কোন সংবাদ পাওয়া যায়নি</h3>
                    <p class="text-muted">এই বিভাগে এখনো কোন সংবাদ প্রকাশিত হয়নি।</p>
                    <a href="<?= SITE_URL ?>" class="btn btn-primary">হোমে ফিরে যান</a>
                </div>
            <?php else: ?>
                <!-- Posts Grid -->
                <div class="posts-grid">
                    <div class="row">
                        <?php foreach ($posts as $index => $post): ?>
                            <div class="col-md-6 mb-4">
                                <article class="news-card card h-100">
                                    <?php if ($post['featured_image']): ?>
                                        <div class="position-relative">
                                            <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                                                 class="card-img-top" alt="<?= e($post['title_bn']) ?>">
                                            
                                            <!-- Post badges -->
                                            <div class="position-absolute top-0 start-0 m-2">
                                                <?php if ($post['is_featured']): ?>
                                                    <span class="badge bg-warning text-dark me-1">ফিচার্ড</span>
                                                <?php endif; ?>
                                                <?php if ($post['is_breaking']): ?>
                                                    <span class="badge bg-danger">ব্রেকিং</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <a href="post.php?slug=<?= $post['slug'] ?>" 
                                               class="text-decoration-none">
                                                <?= e($post['title_bn']) ?>
                                            </a>
                                        </h5>
                                        
                                        <?php if ($post['excerpt']): ?>
                                            <p class="card-text text-muted">
                                                <?= e(truncateText($post['excerpt'], 120)) ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="card-footer bg-transparent">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i><?= e($post['author_name']) ?>
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i><?= timeAgo($post['published_at']) ?>
                                            </small>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i><?= number_format($post['view_count']) ?> ভিউ
                                            </small>
                                        </div>
                                    </div>
                                </article>
                            </div>
                            
                            <!-- Ad placeholder after every 6 posts -->
                            <?php if (($index + 1) % 6 === 0): ?>
                                <div class="col-12 mb-4">
                                    <div class="ad-placeholder text-center py-4 bg-light rounded">
                                        <p class="text-muted mb-0">Advertisement</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Posts pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <!-- Previous Page -->
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="category.php?slug=<?= $slug ?>&page=<?= $page - 1 ?>">
                                        <i class="fas fa-chevron-left me-1"></i>পূর্ববর্তী
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);
                            
                            if ($start_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="category.php?slug=<?= $slug ?>&page=1">1</a>
                                </li>
                                <?php if ($start_page > 2): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="category.php?slug=<?= $slug ?>&page=<?= $i ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="category.php?slug=<?= $slug ?>&page=<?= $total_pages ?>"><?= $total_pages ?></a>
                                </li>
                            <?php endif; ?>
                            
                            <!-- Next Page -->
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="category.php?slug=<?= $slug ?>&page=<?= $page + 1 ?>">
                                        পরবর্তী<i class="fas fa-chevron-right ms-1"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Other Categories -->
            <div class="sidebar-widget mb-4">
                <h4 class="widget-title">
                    <i class="fas fa-tags text-info me-2"></i>অন্যান্য বিভাগ
                </h4>
                <div class="categories-list">
                    <?php 
                    $all_categories = getCategories();
                    foreach ($all_categories as $cat): 
                        if ($cat['slug'] !== $category['slug']):
                    ?>
                        <a href="category.php?slug=<?= $cat['slug'] ?>" 
                           class="category-item d-flex align-items-center justify-content-between text-decoration-none mb-2 p-3 rounded">
                            <div class="d-flex align-items-center">
                                <?php if ($cat['icon']): ?>
                                    <img src="<?= UPLOAD_URL . $cat['icon'] ?>" 
                                         width="24" height="24" class="me-2" alt="<?= e($cat['name_bn']) ?>">
                                <?php else: ?>
                                    <div class="category-color-box me-2" 
                                         style="width: 24px; height: 24px; background-color: <?= e($cat['color']) ?>; border-radius: 4px;"></div>
                                <?php endif; ?>
                                <span class="fw-medium"><?= e($cat['name_bn']) ?></span>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </a>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
            </div>
            
            <!-- Popular Posts -->
            <?php 
            $popular_posts = getPopularPosts(5);
            if (!empty($popular_posts)): 
            ?>
                <div class="sidebar-widget mb-4">
                    <h4 class="widget-title">
                        <i class="fas fa-fire text-danger me-2"></i>জনপ্রিয় সংবাদ
                    </h4>
                    <div class="popular-posts">
                        <?php foreach ($popular_posts as $index => $popular): ?>
                            <div class="popular-post-item d-flex mb-3">
                                <div class="popular-number me-3">
                                    <span class="badge bg-primary"><?= $index + 1 ?></span>
                                </div>
                                <?php if ($popular['featured_image']): ?>
                                    <div class="popular-image me-3">
                                        <img src="<?= UPLOAD_URL . $popular['featured_image'] ?>" 
                                             class="rounded" width="80" height="60" 
                                             style="object-fit: cover;" alt="<?= e($popular['title_bn']) ?>">
                                    </div>
                                <?php endif; ?>
                                <div class="popular-content flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="post.php?slug=<?= $popular['slug'] ?>" 
                                           class="text-decoration-none">
                                            <?= e(truncateText($popular['title_bn'], 60)) ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i><?= number_format($popular['view_count']) ?> ভিউ
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Ad Placeholder -->
            <div class="sidebar-widget mb-4">
                <div class="ad-placeholder text-center py-5 bg-light rounded">
                    <p class="text-muted mb-0">Advertisement</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/frontend_footer.php'; ?>
