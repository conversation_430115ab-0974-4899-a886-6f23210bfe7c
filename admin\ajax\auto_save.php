<?php
/**
 * Auto Save Handler
 * Bengali News CMS
 */

require_once '../../config/config.php';
requireLogin();

header('Content-Type: application/json');

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['auto_save'])) {
    $response['message'] = 'Invalid request';
    echo json_encode($response);
    exit;
}

try {
    $post_id = $_POST['id'] ?? null;
    $title = cleanInput($_POST['title'] ?? '');
    $title_bn = cleanInput($_POST['title_bn'] ?? '');
    $excerpt = cleanInput($_POST['excerpt'] ?? '');
    $content = $_POST['content'] ?? '';
    $category_id = (int)($_POST['category_id'] ?? 0);
    $meta_title = cleanInput($_POST['meta_title'] ?? '');
    $meta_description = cleanInput($_POST['meta_description'] ?? '');
    $meta_keywords = cleanInput($_POST['meta_keywords'] ?? '');
    
    // Generate slug
    $slug = generateSlug($title_bn ?: $title);
    
    if ($post_id) {
        // Update existing post
        $stmt = $db->prepare("SELECT author_id FROM posts WHERE id = ?");
        $stmt->execute([$post_id]);
        $post = $stmt->fetch();
        
        if (!$post) {
            throw new Exception('Post not found');
        }
        
        if (!hasPermission('admin') && $post['author_id'] != $_SESSION['user_id']) {
            throw new Exception('Permission denied');
        }
        
        $sql = "UPDATE posts SET title = ?, title_bn = ?, slug = ?, excerpt = ?, content = ?, 
                               category_id = ?, meta_title = ?, meta_description = ?, meta_keywords = ?, 
                               updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $title, $title_bn, $slug, $excerpt, $content, $category_id,
            $meta_title, $meta_description, $meta_keywords, $post_id
        ]);
        
    } else {
        // Create new draft post
        if (empty($title_bn) && empty($title)) {
            throw new Exception('Title is required');
        }
        
        $sql = "INSERT INTO posts (title, title_bn, slug, excerpt, content, category_id, author_id, 
                                 status, meta_title, meta_description, meta_keywords) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 'draft', ?, ?, ?)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $title, $title_bn, $slug, $excerpt, $content, $category_id, $_SESSION['user_id'],
            $meta_title, $meta_description, $meta_keywords
        ]);
        
        $response['post_id'] = $db->lastInsertId();
    }
    
    $response['success'] = true;
    $response['message'] = 'Auto-saved successfully';
    $response['timestamp'] = date('Y-m-d H:i:s');
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?>
