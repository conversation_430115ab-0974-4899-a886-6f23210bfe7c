<?php
/**
 * Media Library Management
 * Bengali News CMS
 */

require_once '../config/config.php';
requireLogin();

$action = $_GET['action'] ?? 'list';
$media_id = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token';
    } else {
        if ($action === 'upload') {
            if (!isset($_FILES['files'])) {
                $error = 'No files selected';
            } else {
                $files = $_FILES['files'];
                $uploaded_count = 0;
                $errors = [];
                
                // Handle multiple file uploads
                for ($i = 0; $i < count($files['name']); $i++) {
                    if ($files['error'][$i] === UPLOAD_ERR_OK) {
                        $file = [
                            'name' => $files['name'][$i],
                            'type' => $files['type'][$i],
                            'tmp_name' => $files['tmp_name'][$i],
                            'error' => $files['error'][$i],
                            'size' => $files['size'][$i]
                        ];
                        
                        // Validate file
                        $allowed_types = array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_FILE_TYPES);
                        $validation = validateFileUpload($file, $allowed_types);
                        
                        if ($validation['success']) {
                            // Determine upload directory
                            $is_image = in_array($validation['extension'], ALLOWED_IMAGE_TYPES);
                            $upload_dir = $is_image ? 'images/' : 'documents/';
                            
                            // Generate secure filename
                            $filename = generateSecureFilename($file['name']);
                            $upload_path = UPLOAD_PATH . $upload_dir . $filename;
                            $relative_path = $upload_dir . $filename;
                            
                            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                                // Save to media library
                                $alt_text = cleanInput($_POST['alt_text'][$i] ?? '');
                                $caption = cleanInput($_POST['caption'][$i] ?? '');
                                
                                $stmt = $db->prepare("INSERT INTO media (filename, original_name, file_path, file_size, mime_type, alt_text, caption, uploaded_by) 
                                                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                                $stmt->execute([
                                    $filename,
                                    $file['name'],
                                    $relative_path,
                                    $file['size'],
                                    $validation['mime_type'],
                                    $alt_text,
                                    $caption,
                                    $_SESSION['user_id']
                                ]);
                                
                                $uploaded_count++;
                            } else {
                                $errors[] = "Failed to upload {$file['name']}";
                            }
                        } else {
                            $errors[] = "{$file['name']}: {$validation['message']}";
                        }
                    }
                }
                
                if ($uploaded_count > 0) {
                    $success = "Successfully uploaded $uploaded_count file(s)";
                }
                if (!empty($errors)) {
                    $error = implode('<br>', $errors);
                }
            }
            
        } elseif ($action === 'edit' && $media_id) {
            $alt_text = cleanInput($_POST['alt_text']);
            $caption = cleanInput($_POST['caption']);
            
            $stmt = $db->prepare("UPDATE media SET alt_text = ?, caption = ? WHERE id = ?");
            if ($stmt->execute([$alt_text, $caption, $media_id])) {
                $success = 'Media information updated successfully';
            } else {
                $error = 'Failed to update media information';
            }
            
        } elseif ($action === 'delete' && $media_id) {
            // Get media info
            $stmt = $db->prepare("SELECT * FROM media WHERE id = ?");
            $stmt->execute([$media_id]);
            $media = $stmt->fetch();
            
            if ($media) {
                // Delete file
                $file_path = UPLOAD_PATH . $media['file_path'];
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
                
                // Delete from database
                $stmt = $db->prepare("DELETE FROM media WHERE id = ?");
                if ($stmt->execute([$media_id])) {
                    $success = 'Media deleted successfully';
                } else {
                    $error = 'Failed to delete media';
                }
            } else {
                $error = 'Media not found';
            }
            
        } elseif ($action === 'bulk_delete') {
            $media_ids = $_POST['media_ids'] ?? [];
            if (!empty($media_ids)) {
                $placeholders = str_repeat('?,', count($media_ids) - 1) . '?';
                
                // Get media files to delete
                $stmt = $db->prepare("SELECT file_path FROM media WHERE id IN ($placeholders)");
                $stmt->execute($media_ids);
                $files = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                // Delete files
                foreach ($files as $file_path) {
                    $full_path = UPLOAD_PATH . $file_path;
                    if (file_exists($full_path)) {
                        unlink($full_path);
                    }
                }
                
                // Delete from database
                $stmt = $db->prepare("DELETE FROM media WHERE id IN ($placeholders)");
                $stmt->execute($media_ids);
                
                $success = 'Selected media files deleted successfully';
            }
        }
    }
}

// Get media data for edit
$media_data = null;
if ($action === 'edit' && $media_id) {
    $stmt = $db->prepare("SELECT * FROM media WHERE id = ?");
    $stmt->execute([$media_id]);
    $media_data = $stmt->fetch();
}

$page_title = ($action === 'upload') ? 'Upload Media' : (($action === 'edit') ? 'Edit Media' : 'Media Library');
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($action === 'list'): ?>
        <!-- Media Library -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Media Library</h1>
                    <div>
                        <button type="button" class="btn btn-danger me-2" id="bulkDeleteBtn" style="display: none;">
                            <i class="fas fa-trash me-2"></i>Delete Selected
                        </button>
                        <a href="media.php?action=upload" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Upload Files
                        </a>
                    </div>
                </div>
                
                <!-- Filter Options -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="typeFilter">
                                    <option value="">All Types</option>
                                    <option value="image">Images</option>
                                    <option value="document">Documents</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="dateFilter">
                                    <option value="">All Dates</option>
                                    <option value="today">Today</option>
                                    <option value="week">This Week</option>
                                    <option value="month">This Month</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchFilter" placeholder="Search files...">
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-outline-secondary active" id="gridView">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="listView">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <?php
                        // Get media files with pagination
                        $page = $_GET['page'] ?? 1;
                        $limit = 24;
                        $offset = ($page - 1) * $limit;
                        
                        $sql = "SELECT m.*, u.full_name as uploader_name 
                                FROM media m 
                                JOIN users u ON m.uploaded_by = u.id 
                                ORDER BY m.created_at DESC 
                                LIMIT ? OFFSET ?";
                        
                        $stmt = $db->prepare($sql);
                        $stmt->execute([$limit, $offset]);
                        $media_files = $stmt->fetchAll();
                        ?>
                        
                        <?php if (empty($media_files)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                <h5>No media files found</h5>
                                <p class="text-muted">Upload your first media file to get started</p>
                                <a href="media.php?action=upload" class="btn btn-primary">Upload Files</a>
                            </div>
                        <?php else: ?>
                            <form id="bulkForm" method="POST">
                                <?= getCSRFTokenInput() ?>
                                <input type="hidden" name="action" value="bulk_delete">
                                
                                <!-- Grid View -->
                                <div id="mediaGrid" class="row">
                                    <?php foreach ($media_files as $media): ?>
                                        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-4 media-item" 
                                             data-type="<?= strpos($media['mime_type'], 'image/') === 0 ? 'image' : 'document' ?>"
                                             data-date="<?= $media['created_at'] ?>"
                                             data-name="<?= strtolower($media['original_name']) ?>">
                                            <div class="card h-100">
                                                <div class="position-relative">
                                                    <input type="checkbox" name="media_ids[]" value="<?= $media['id'] ?>" 
                                                           class="form-check-input position-absolute top-0 start-0 m-2 media-checkbox">
                                                    
                                                    <?php if (strpos($media['mime_type'], 'image/') === 0): ?>
                                                        <img src="<?= UPLOAD_URL . $media['file_path'] ?>" 
                                                             class="card-img-top" style="height: 150px; object-fit: cover;" 
                                                             alt="<?= e($media['alt_text']) ?>">
                                                    <?php else: ?>
                                                        <div class="card-img-top d-flex align-items-center justify-content-center bg-light" 
                                                             style="height: 150px;">
                                                            <i class="fas fa-file fa-3x text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="card-body p-2">
                                                    <h6 class="card-title mb-1" title="<?= e($media['original_name']) ?>">
                                                        <?= e(truncateText($media['original_name'], 20)) ?>
                                                    </h6>
                                                    <small class="text-muted d-block"><?= formatFileSize($media['file_size']) ?></small>
                                                    <small class="text-muted d-block"><?= timeAgo($media['created_at']) ?></small>
                                                </div>
                                                
                                                <div class="card-footer p-2">
                                                    <div class="btn-group w-100 btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary" 
                                                                onclick="viewMedia(<?= $media['id'] ?>)" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <a href="media.php?action=edit&id=<?= $media['id'] ?>" 
                                                           class="btn btn-outline-secondary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-success" 
                                                                onclick="copyURL('<?= UPLOAD_URL . $media['file_path'] ?>')" title="Copy URL">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                        <a href="media.php?action=delete&id=<?= $media['id'] ?>" 
                                                           class="btn btn-outline-danger" title="Delete"
                                                           onclick="return confirmDelete('Are you sure you want to delete this file?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <!-- List View (hidden by default) -->
                                <div id="mediaList" class="table-responsive" style="display: none;">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th width="30">
                                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                                </th>
                                                <th>File</th>
                                                <th>Type</th>
                                                <th>Size</th>
                                                <th>Uploaded By</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($media_files as $media): ?>
                                                <tr class="media-item" 
                                                    data-type="<?= strpos($media['mime_type'], 'image/') === 0 ? 'image' : 'document' ?>"
                                                    data-date="<?= $media['created_at'] ?>"
                                                    data-name="<?= strtolower($media['original_name']) ?>">
                                                    <td>
                                                        <input type="checkbox" name="media_ids[]" value="<?= $media['id'] ?>" 
                                                               class="form-check-input media-checkbox">
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if (strpos($media['mime_type'], 'image/') === 0): ?>
                                                                <img src="<?= UPLOAD_URL . $media['file_path'] ?>" 
                                                                     class="me-2 rounded" width="40" height="40" 
                                                                     style="object-fit: cover;" alt="Thumbnail">
                                                            <?php else: ?>
                                                                <div class="me-2 bg-light rounded d-flex align-items-center justify-content-center" 
                                                                     style="width: 40px; height: 40px;">
                                                                    <i class="fas fa-file text-muted"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                            <div>
                                                                <div class="fw-bold"><?= e($media['original_name']) ?></div>
                                                                <?php if ($media['alt_text']): ?>
                                                                    <small class="text-muted"><?= e($media['alt_text']) ?></small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?= e($media['mime_type']) ?></td>
                                                    <td><?= formatFileSize($media['file_size']) ?></td>
                                                    <td><?= e($media['uploader_name']) ?></td>
                                                    <td><?= timeAgo($media['created_at']) ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button type="button" class="btn btn-outline-primary" 
                                                                    onclick="viewMedia(<?= $media['id'] ?>)" title="View">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <a href="media.php?action=edit&id=<?= $media['id'] ?>" 
                                                               class="btn btn-outline-secondary" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-outline-success" 
                                                                    onclick="copyURL('<?= UPLOAD_URL . $media['file_path'] ?>')" title="Copy URL">
                                                                <i class="fas fa-copy"></i>
                                                            </button>
                                                            <a href="media.php?action=delete&id=<?= $media['id'] ?>" 
                                                               class="btn btn-outline-danger" title="Delete"
                                                               onclick="return confirmDelete('Are you sure you want to delete this file?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
    <?php elseif ($action === 'upload'): ?>
        <!-- Upload Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Upload Media Files</h1>
                    <a href="media.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Media Library
                    </a>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?= e($success) ?></div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Select Files to Upload</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <?= getCSRFTokenInput() ?>
                            
                            <div class="mb-4">
                                <label for="files" class="form-label">Choose Files</label>
                                <input type="file" class="form-control" id="files" name="files[]" multiple 
                                       accept="image/*,.pdf,.doc,.docx,.txt" required>
                                <div class="form-text">
                                    Allowed types: Images (JPG, PNG, GIF, WebP), Documents (PDF, DOC, DOCX, TXT)<br>
                                    Maximum file size: <?= formatFileSize(MAX_FILE_SIZE) ?>
                                </div>
                            </div>
                            
                            <div id="filePreview" class="row"></div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>Upload Files
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
    <?php elseif ($action === 'edit' && $media_data): ?>
        <!-- Edit Media Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Edit Media</h1>
                    <a href="media.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Media Library
                    </a>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= e($error) ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?= e($success) ?></div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">File Preview</h5>
                            </div>
                            <div class="card-body text-center">
                                <?php if (strpos($media_data['mime_type'], 'image/') === 0): ?>
                                    <img src="<?= UPLOAD_URL . $media_data['file_path'] ?>" 
                                         class="img-fluid rounded" alt="<?= e($media_data['alt_text']) ?>">
                                <?php else: ?>
                                    <div class="py-5">
                                        <i class="fas fa-file fa-5x text-muted mb-3"></i>
                                        <h5><?= e($media_data['original_name']) ?></h5>
                                        <p class="text-muted"><?= e($media_data['mime_type']) ?></p>
                                        <a href="<?= UPLOAD_URL . $media_data['file_path'] ?>" 
                                           class="btn btn-primary" target="_blank">
                                            <i class="fas fa-download me-2"></i>Download
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">File Information</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <?= getCSRFTokenInput() ?>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">File Name</label>
                                        <input type="text" class="form-control" value="<?= e($media_data['original_name']) ?>" readonly>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">File Size</label>
                                        <input type="text" class="form-control" value="<?= formatFileSize($media_data['file_size']) ?>" readonly>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">File Type</label>
                                        <input type="text" class="form-control" value="<?= e($media_data['mime_type']) ?>" readonly>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="alt_text" class="form-label">Alt Text</label>
                                        <input type="text" class="form-control" id="alt_text" name="alt_text" 
                                               value="<?= e($media_data['alt_text']) ?>"
                                               placeholder="Describe this image for accessibility">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="caption" class="form-label">Caption</label>
                                        <textarea class="form-control" id="caption" name="caption" rows="3"
                                                  placeholder="Optional caption for this media"><?= e($media_data['caption']) ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">File URL</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="fileURL" 
                                                   value="<?= UPLOAD_URL . $media_data['file_path'] ?>" readonly>
                                            <button type="button" class="btn btn-outline-secondary" 
                                                    onclick="copyURL('<?= UPLOAD_URL . $media_data['file_path'] ?>')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update Media
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Media Viewer Modal -->
<div class="modal fade" id="mediaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Media Viewer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center" id="mediaModalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<?php
$custom_scripts = "
<script>
    // File upload preview
    document.getElementById('files').addEventListener('change', function() {
        const preview = document.getElementById('filePreview');
        preview.innerHTML = '';
        
        Array.from(this.files).forEach((file, index) => {
            const col = document.createElement('div');
            col.className = 'col-md-6 mb-3';
            
            const isImage = file.type.startsWith('image/');
            
            col.innerHTML = `
                <div class='card'>
                    <div class='card-body'>
                        <h6 class='card-title'>\${file.name}</h6>
                        <p class='card-text'>\${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                        <div class='mb-2'>
                            <input type='text' class='form-control form-control-sm' name='alt_text[]' placeholder='Alt text (for images)'>
                        </div>
                        <div>
                            <input type='text' class='form-control form-control-sm' name='caption[]' placeholder='Caption (optional)'>
                        </div>
                    </div>
                </div>
            `;
            
            preview.appendChild(col);
        });
    });
    
    // View/List toggle
    document.getElementById('gridView').addEventListener('click', function() {
        document.getElementById('mediaGrid').style.display = 'block';
        document.getElementById('mediaList').style.display = 'none';
        this.classList.add('active');
        document.getElementById('listView').classList.remove('active');
    });
    
    document.getElementById('listView').addEventListener('click', function() {
        document.getElementById('mediaGrid').style.display = 'none';
        document.getElementById('mediaList').style.display = 'block';
        this.classList.add('active');
        document.getElementById('gridView').classList.remove('active');
    });
    
    // Bulk selection
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.media-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        toggleBulkDeleteButton();
    });
    
    document.querySelectorAll('.media-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkDeleteButton);
    });
    
    function toggleBulkDeleteButton() {
        const checkedBoxes = document.querySelectorAll('.media-checkbox:checked');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        
        if (checkedBoxes.length > 0) {
            bulkDeleteBtn.style.display = 'inline-block';
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }
    
    document.getElementById('bulkDeleteBtn').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.media-checkbox:checked');
        if (checkedBoxes.length === 0) {
            alert('Please select files to delete');
            return;
        }
        
        if (confirm('Are you sure you want to delete the selected files?')) {
            document.getElementById('bulkForm').submit();
        }
    });
    
    // Media viewer
    function viewMedia(mediaId) {
        // Fetch media details and show in modal
        fetch('ajax/get_media.php?id=' + mediaId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const modal = new bootstrap.Modal(document.getElementById('mediaModal'));
                    document.getElementById('mediaModalBody').innerHTML = data.html;
                    modal.show();
                }
            });
    }
    
    // Copy URL function
    function copyURL(url) {
        copyToClipboard(url);
    }
    
    // Filtering
    document.getElementById('typeFilter').addEventListener('change', filterMedia);
    document.getElementById('dateFilter').addEventListener('change', filterMedia);
    document.getElementById('searchFilter').addEventListener('input', filterMedia);
    
    function filterMedia() {
        const typeFilter = document.getElementById('typeFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
        
        document.querySelectorAll('.media-item').forEach(item => {
            let show = true;
            
            // Type filter
            if (typeFilter && item.dataset.type !== typeFilter) {
                show = false;
            }
            
            // Search filter
            if (searchFilter && !item.dataset.name.includes(searchFilter)) {
                show = false;
            }
            
            // Date filter
            if (dateFilter) {
                const itemDate = new Date(item.dataset.date);
                const now = new Date();
                
                switch (dateFilter) {
                    case 'today':
                        if (itemDate.toDateString() !== now.toDateString()) {
                            show = false;
                        }
                        break;
                    case 'week':
                        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                        if (itemDate < weekAgo) {
                            show = false;
                        }
                        break;
                    case 'month':
                        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                        if (itemDate < monthAgo) {
                            show = false;
                        }
                        break;
                }
            }
            
            item.style.display = show ? 'block' : 'none';
        });
    }
</script>
";

include 'includes/footer.php';
?>
