version: '3.8'

services:
  web:
    build: .
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./uploads:/var/www/html/uploads
      - ./cache:/var/www/html/cache
    depends_on:
      - db
    environment:
      - DB_HOST=db
      - DB_NAME=bengali_news
      - DB_USER=root
      - DB_PASS=password
    networks:
      - bengali-news-network

  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: bengali_news
      MYSQL_USER: bengali_user
      MYSQL_PASSWORD: bengali_pass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - bengali-news-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_USER: root
      PMA_PASSWORD: password
    depends_on:
      - db
    networks:
      - bengali-news-network

volumes:
  mysql_data:

networks:
  bengali-news-network:
    driver: bridge
