# 🚀 Quick Start Guide - Bengali News CMS

Get your Bengali News CMS up and running in minutes!

## 🎯 Choose Your Setup Method

### **Method 1: Docker (Recommended) 🐳**

**Prerequisites**: Dock<PERSON> and <PERSON>er Compose installed

```bash
# 1. Run the start script
./start-server.sh        # Linux/Mac
start-server.bat         # Windows

# 2. Wait for containers to start (30-60 seconds)

# 3. Open your browser and go to:
http://localhost:8080/install.php
```

**Database credentials for installation:**
- Host: `db`
- Database: `bengali_news`
- Username: `root`
- Password: `password`

---

### **Method 2: XAMPP (Beginner Friendly) 📦**

1. **Download & Install XAMPP**
   - Go to https://www.apachefriends.org/
   - Download for your OS and install

2. **Start Services**
   - Open XAMPP Control Panel
   - Start Apache and MySQL

3. **Setup CMS**
   ```bash
   # Copy CMS files to:
   # Windows: C:\xampp\htdocs\bengali-news-cms\
   # Mac: /Applications/XAMPP/htdocs/bengali-news-cms/
   # Linux: /opt/lampp/htdocs/bengali-news-cms/
   ```

4. **Create Database**
   - Open http://localhost/phpmyadmin
   - Create new database: `bengali_news`

5. **Install CMS**
   - Go to http://localhost/bengali-news-cms/install.php
   - Use database credentials:
     - Host: `localhost`
     - Database: `bengali_news`
     - Username: `root`
     - Password: (leave empty or use your MySQL password)

---

### **Method 3: PHP Built-in Server (Quick Test) ⚡**

**Prerequisites**: PHP 7.4+ installed

```bash
# 1. Run the start script
./start-server.sh        # Linux/Mac
start-server.bat         # Windows

# 2. Open browser:
http://localhost:8000/install.php
```

**Note**: You'll need MySQL installed separately for this method.

---

## 🔧 Installation Steps

Once your server is running:

### **Step 1: Database Configuration**
- Enter your database credentials
- Test connection
- Continue to next step

### **Step 2: Admin Account**
- Create your admin username/password
- Enter site title and description
- Complete installation

### **Step 3: First Login**
- Go to `/admin/login.php`
- Login with your admin credentials
- Start customizing your site!

---

## 🎨 Quick Customization

### **1. Site Settings**
- Go to Admin → Settings
- Update site title, description
- Configure contact information

### **2. Create Categories**
- Go to Admin → Categories
- Add news categories (Politics, Sports, etc.)
- Set colors and icons

### **3. Add Your First Post**
- Go to Admin → Posts → Add New
- Write your first news article
- Publish it!

### **4. Customize Pages**
- Go to Admin → Pages
- Edit About Us, Contact pages
- Add your information

---

## 🌐 Access URLs

After installation:

- **Frontend**: http://localhost:8080 (Docker) or http://localhost:8000 (PHP)
- **Admin Panel**: `/admin/login.php`
- **phpMyAdmin**: http://localhost:8081 (Docker only)

---

## 🆘 Troubleshooting

### **Port Already in Use**
```bash
# Docker: Change ports in docker-compose.yml
ports:
  - "8090:80"  # Change 8080 to 8090

# PHP: Use different port
php -S localhost:8001
```

### **Permission Issues**
```bash
# Linux/Mac
chmod -R 775 uploads cache config

# Windows: Run as Administrator
```

### **Database Connection Failed**
- Check if MySQL is running
- Verify database credentials
- Ensure database exists

### **Can't Access Admin Panel**
- Make sure you completed installation
- Check if `/admin/login.php` exists
- Clear browser cache

---

## 📱 Default Admin Credentials

After installation, you'll have:
- **Username**: (what you set during installation)
- **Password**: (what you set during installation)
- **Role**: Administrator

---

## 🎯 Next Steps

1. **Content Creation**
   - Add categories and tags
   - Create your first news posts
   - Upload media files

2. **Customization**
   - Update site branding
   - Configure SEO settings
   - Set up social media links

3. **Security**
   - Change default passwords
   - Enable SSL (for production)
   - Set up regular backups

4. **Performance**
   - Enable caching
   - Optimize images
   - Set up CDN (for production)

---

## 📚 Need Help?

- **Documentation**: See README.md for detailed information
- **Deployment**: See DEPLOYMENT.md for production setup
- **Issues**: Check the troubleshooting section above

---

**🎉 Congratulations! Your Bengali News CMS is ready to use!**

Start creating amazing Bengali content and share news with your community!
