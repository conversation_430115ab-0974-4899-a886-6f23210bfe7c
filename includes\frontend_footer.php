    <!-- Footer -->
    <footer class="bg-dark text-white mt-5">
        <div class="container py-5">
            <div class="row">
                <!-- About Section -->
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-newspaper me-2"></i><?= e(getSetting('site_title', 'Bengali News CMS')) ?>
                    </h5>
                    <p class="text-light">
                        <?= e(getSetting('site_description', 'আমরা সর্বশেষ এবং নির্ভরযোগ্য বাংলা সংবাদ প্রদান করি। আমাদের লক্ষ্য হল সত্য ও নিরপেক্ষ সংবাদ পরিবেশন করা।')) ?>
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3 fs-5"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3 fs-5"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3 fs-5"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="text-light me-3 fs-5"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light fs-5"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">দ্রুত লিংক</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="<?= SITE_URL ?>" class="text-light text-decoration-none">হোম</a></li>
                        <li class="mb-2"><a href="about.php" class="text-light text-decoration-none">আমাদের সম্পর্কে</a></li>
                        <li class="mb-2"><a href="contact.php" class="text-light text-decoration-none">যোগাযোগ</a></li>
                        <li class="mb-2"><a href="privacy.php" class="text-light text-decoration-none">গোপনীয়তা নীতি</a></li>
                        <li class="mb-2"><a href="terms.php" class="text-light text-decoration-none">ব্যবহারের শর্তাবলী</a></li>
                    </ul>
                </div>
                
                <!-- Categories -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3">বিভাগসমূহ</h6>
                    <ul class="list-unstyled">
                        <?php 
                        $footer_categories = array_slice(getCategories(), 0, 6);
                        foreach ($footer_categories as $category): 
                        ?>
                            <li class="mb-2">
                                <a href="category.php?slug=<?= $category['slug'] ?>" 
                                   class="text-light text-decoration-none">
                                    <?= e($category['name_bn']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Newsletter -->
                <div class="col-lg-3 mb-4">
                    <h6 class="mb-3">নিউজলেটার</h6>
                    <p class="text-light mb-3">সর্বশেষ সংবাদ পেতে আমাদের নিউজলেটার সাবস্ক্রাইব করুন</p>
                    <form class="newsletter-form" id="footerNewsletterForm">
                        <div class="input-group mb-3">
                            <input type="email" class="form-control" placeholder="আপনার ইমেইল" required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Contact Info -->
                    <div class="contact-info mt-4">
                        <p class="text-light mb-1">
                            <i class="fas fa-envelope me-2"></i>
                            <EMAIL>
                        </p>
                        <p class="text-light mb-1">
                            <i class="fas fa-phone me-2"></i>
                            +880 1234 567890
                        </p>
                        <p class="text-light mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            ঢাকা, বাংলাদেশ
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Copyright -->
        <div class="bg-black py-3">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="text-light mb-0">
                            &copy; <?= date('Y') ?> <?= e(getSetting('site_title', 'Bengali News CMS')) ?>. সকল অধিকার সংরক্ষিত।
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="text-light mb-0">
                            Powered by <a href="#" class="text-primary text-decoration-none">Bengali News CMS</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button class="btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle" 
            id="backToTop" style="display: none; width: 50px; height: 50px; z-index: 1000;">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Current time display
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('bn-BD', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();
        
        // Back to top button
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
            } else {
                backToTop.style.display = 'none';
            }
        });
        
        document.getElementById('backToTop').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // Load more posts functionality
        let currentPage = 1;
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                currentPage++;
                
                fetch(`ajax/load_more_posts.php?page=${currentPage}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.html) {
                            // Insert new posts before the load more button
                            const latestNews = document.querySelector('.latest-news .row');
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = data.html;
                            
                            while (tempDiv.firstChild) {
                                latestNews.insertBefore(tempDiv.firstChild, loadMoreBtn.parentElement);
                            }
                            
                            // Hide button if no more posts
                            if (!data.hasMore) {
                                loadMoreBtn.style.display = 'none';
                            }
                        } else {
                            loadMoreBtn.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading more posts:', error);
                    });
            });
        }
        
        // Newsletter form submission
        document.getElementById('footerNewsletterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            
            fetch('ajax/newsletter_subscribe.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'email=' + encodeURIComponent(email)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('ধন্যবাদ! আপনি সফলভাবে নিউজলেটার সাবস্ক্রাইব করেছেন।');
                    this.reset();
                } else {
                    alert('দুঃখিত! কিছু সমস্যা হয়েছে। আবার চেষ্টা করুন।');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('দুঃখিত! কিছু সমস্যা হয়েছে। আবার চেষ্টা করুন।');
            });
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Image lazy loading
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
        
        // Search functionality enhancement
        const searchForm = document.querySelector('.search-box form');
        if (searchForm) {
            const searchInput = searchForm.querySelector('input[name="q"]');
            
            // Add search suggestions (you can implement this with AJAX)
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length > 2) {
                    // Implement search suggestions here
                    // fetch(`ajax/search_suggestions.php?q=${encodeURIComponent(query)}`)
                }
            });
        }
        
        // Social sharing functionality
        function shareOnSocial(platform, url, title) {
            let shareUrl = '';
            
            switch (platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
                    break;
                case 'whatsapp':
                    shareUrl = `https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`;
                    break;
            }
            
            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
        }
        
        // Add click tracking for analytics
        document.addEventListener('click', function(e) {
            const link = e.target.closest('a');
            if (link && link.href) {
                // Track clicks for analytics
                // You can send data to your analytics service here
            }
        });
        
        // Performance optimization: Preload critical resources
        function preloadCriticalResources() {
            const criticalImages = document.querySelectorAll('img[data-critical]');
            criticalImages.forEach(img => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = img.src || img.dataset.src;
                document.head.appendChild(link);
            });
        }
        
        // Initialize on DOM ready
        document.addEventListener('DOMContentLoaded', function() {
            preloadCriticalResources();
        });
    </script>
    
    <!-- Custom page scripts -->
    <?php if (isset($custom_scripts)): ?>
        <?= $custom_scripts ?>
    <?php endif; ?>
    
</body>
</html>
