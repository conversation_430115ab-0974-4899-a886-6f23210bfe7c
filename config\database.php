<?php
/**
 * Database Configuration
 * Bengali News CMS
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'bengali_news_cms';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    public function connect() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            echo 'Connection Error: ' . $e->getMessage();
        }

        return $this->conn;
    }

    public function createTables() {
        $sql = "
        -- Users table for admin, editors, writers
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'editor', 'writer') DEFAULT 'writer',
            profile_image VARCHAR(255) DEFAULT NULL,
            bio TEXT DEFAULT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Categories table
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            name_bn VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT DEFAULT NULL,
            icon VARCHAR(255) DEFAULT NULL,
            image VARCHAR(255) DEFAULT NULL,
            color VARCHAR(7) DEFAULT '#000000',
            sort_order INT DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Tags table
        CREATE TABLE IF NOT EXISTS tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            name_bn VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Posts table
        CREATE TABLE IF NOT EXISTS posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            title_bn VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            excerpt TEXT DEFAULT NULL,
            content LONGTEXT NOT NULL,
            featured_image VARCHAR(255) DEFAULT NULL,
            category_id INT NOT NULL,
            author_id INT NOT NULL,
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            is_featured BOOLEAN DEFAULT FALSE,
            is_breaking BOOLEAN DEFAULT FALSE,
            view_count INT DEFAULT 0,
            meta_title VARCHAR(255) DEFAULT NULL,
            meta_description TEXT DEFAULT NULL,
            meta_keywords TEXT DEFAULT NULL,
            published_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
            FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_status (status),
            INDEX idx_published_at (published_at),
            INDEX idx_category (category_id),
            INDEX idx_featured (is_featured),
            INDEX idx_breaking (is_breaking),
            INDEX idx_view_count (view_count)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Post tags relationship table
        CREATE TABLE IF NOT EXISTS post_tags (
            post_id INT NOT NULL,
            tag_id INT NOT NULL,
            PRIMARY KEY (post_id, tag_id),
            FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
            FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Pages table for static pages
        CREATE TABLE IF NOT EXISTS pages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            title_bn VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            content LONGTEXT NOT NULL,
            meta_title VARCHAR(255) DEFAULT NULL,
            meta_description TEXT DEFAULT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Media library table
        CREATE TABLE IF NOT EXISTS media (
            id INT AUTO_INCREMENT PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            alt_text VARCHAR(255) DEFAULT NULL,
            caption TEXT DEFAULT NULL,
            uploaded_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_mime_type (mime_type),
            INDEX idx_uploaded_by (uploaded_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Post views tracking table
        CREATE TABLE IF NOT EXISTS post_views (
            id INT AUTO_INCREMENT PRIMARY KEY,
            post_id INT NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT DEFAULT NULL,
            viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
            INDEX idx_post_viewed (post_id, viewed_at),
            INDEX idx_ip_date (ip_address, viewed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Settings table
        CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value LONGTEXT DEFAULT NULL,
            setting_type ENUM('text', 'textarea', 'number', 'boolean', 'json') DEFAULT 'text',
            description TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- Cache table for performance
        CREATE TABLE IF NOT EXISTS cache (
            cache_key VARCHAR(255) PRIMARY KEY,
            cache_value LONGTEXT NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_expires (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        try {
            $this->conn->exec($sql);
            return true;
        } catch(PDOException $e) {
            echo "Error creating tables: " . $e->getMessage();
            return false;
        }
    }

    public function insertDefaultData() {
        // Insert default admin user
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT IGNORE INTO users (username, email, password, full_name, role) 
                VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'admin')";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$admin_password]);

        // Insert default categories
        $categories = [
            ['জাতীয়', 'National', 'national', 'জাতীয় সংবাদ', '#FF6B6B'],
            ['আন্তর্জাতিক', 'International', 'international', 'আন্তর্জাতিক সংবাদ', '#4ECDC4'],
            ['রাজনীতি', 'Politics', 'politics', 'রাজনৈতিক সংবাদ', '#45B7D1'],
            ['অর্থনীতি', 'Economy', 'economy', 'অর্থনৈতিক সংবাদ', '#96CEB4'],
            ['খেলাধুলা', 'Sports', 'sports', 'খেলাধুলার সংবাদ', '#FFEAA7'],
            ['বিনোদন', 'Entertainment', 'entertainment', 'বিনোদন সংবাদ', '#DDA0DD'],
            ['প্রযুক্তি', 'Technology', 'technology', 'প্রযুক্তি সংবাদ', '#98D8C8']
        ];

        foreach ($categories as $index => $cat) {
            $sql = "INSERT IGNORE INTO categories (name_bn, name, slug, description, color, sort_order) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$cat[0], $cat[1], $cat[2], $cat[3], $cat[4], $index + 1]);
        }

        // Insert default settings
        $settings = [
            ['site_title', 'Bengali News CMS', 'text', 'Website title'],
            ['site_description', 'A modern Bengali news website', 'textarea', 'Website description'],
            ['posts_per_page', '10', 'number', 'Number of posts per page'],
            ['latest_posts_count', '15', 'number', 'Number of latest posts to show'],
            ['popular_posts_count', '7', 'number', 'Number of popular posts to show'],
            ['popular_posts_days', '7', 'number', 'Days to calculate popular posts'],
            ['cache_enabled', '1', 'boolean', 'Enable caching system'],
            ['cache_duration', '3600', 'number', 'Cache duration in seconds']
        ];

        foreach ($settings as $setting) {
            $sql = "INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description) 
                    VALUES (?, ?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($setting);
        }

        return true;
    }
}
?>
