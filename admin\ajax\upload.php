<?php
/**
 * AJAX Upload Handler
 * Bengali News CMS
 */

require_once '../../config/config.php';
requireLogin();

header('Content-Type: application/json');

$response = ['success' => false, 'message' => '', 'url' => ''];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit;
}

$action = $_POST['action'] ?? '';

if ($action === 'upload_image') {
    if (!isset($_FILES['file'])) {
        $response['message'] = 'No file uploaded';
        echo json_encode($response);
        exit;
    }
    
    $file = $_FILES['file'];
    
    // Validate file
    $validation = validateFileUpload($file, ALLOWED_IMAGE_TYPES);
    if (!$validation['success']) {
        $response['message'] = $validation['message'];
        echo json_encode($response);
        exit;
    }
    
    // Generate secure filename
    $filename = generateSecureFilename($file['name']);
    $upload_path = UPLOAD_PATH . 'images/' . $filename;
    $relative_path = 'images/' . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        // Save to media library
        try {
            $stmt = $db->prepare("INSERT INTO media (filename, original_name, file_path, file_size, mime_type, uploaded_by) 
                                 VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $filename,
                $file['name'],
                $relative_path,
                $file['size'],
                $validation['mime_type'],
                $_SESSION['user_id']
            ]);
            
            $response['success'] = true;
            $response['url'] = UPLOAD_URL . $relative_path;
            $response['message'] = 'Image uploaded successfully';
            
        } catch (Exception $e) {
            // Delete uploaded file if database insert fails
            unlink($upload_path);
            $response['message'] = 'Failed to save image information';
        }
    } else {
        $response['message'] = 'Failed to upload file';
    }
    
} elseif ($action === 'upload_file') {
    if (!isset($_FILES['file'])) {
        $response['message'] = 'No file uploaded';
        echo json_encode($response);
        exit;
    }
    
    $file = $_FILES['file'];
    
    // Validate file
    $allowed_types = array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_FILE_TYPES);
    $validation = validateFileUpload($file, $allowed_types);
    if (!$validation['success']) {
        $response['message'] = $validation['message'];
        echo json_encode($response);
        exit;
    }
    
    // Determine upload directory based on file type
    $is_image = in_array($validation['extension'], ALLOWED_IMAGE_TYPES);
    $upload_dir = $is_image ? 'images/' : 'documents/';
    
    // Generate secure filename
    $filename = generateSecureFilename($file['name']);
    $upload_path = UPLOAD_PATH . $upload_dir . $filename;
    $relative_path = $upload_dir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        // Save to media library
        try {
            $stmt = $db->prepare("INSERT INTO media (filename, original_name, file_path, file_size, mime_type, uploaded_by) 
                                 VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $filename,
                $file['name'],
                $relative_path,
                $file['size'],
                $validation['mime_type'],
                $_SESSION['user_id']
            ]);
            
            $response['success'] = true;
            $response['url'] = UPLOAD_URL . $relative_path;
            $response['filename'] = $file['name'];
            $response['size'] = formatFileSize($file['size']);
            $response['message'] = 'File uploaded successfully';
            
        } catch (Exception $e) {
            // Delete uploaded file if database insert fails
            unlink($upload_path);
            $response['message'] = 'Failed to save file information';
        }
    } else {
        $response['message'] = 'Failed to upload file';
    }
    
} else {
    $response['message'] = 'Invalid action';
}

echo json_encode($response);
?>
