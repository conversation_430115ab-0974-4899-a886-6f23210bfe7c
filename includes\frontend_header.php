<?php
/**
 * Frontend Header
 * Bengali News CMS
 */

$site_title = getSetting('site_title', 'Bengali News CMS');
$site_description = getSetting('site_description', 'Latest Bengali news and updates');
$categories = getCategories();
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($page_title) ? e($page_title) : e($site_title) ?></title>
    <meta name="description" content="<?= isset($page_description) ? e($page_description) : e($site_description) ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= isset($page_title) ? e($page_title) : e($site_title) ?>">
    <meta property="og:description" content="<?= isset($page_description) ? e($page_description) : e($site_description) ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= SITE_URL ?>">
    <?php if (isset($page_image)): ?>
        <meta property="og:image" content="<?= $page_image ?>">
    <?php endif; ?>
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= isset($page_title) ? e($page_title) : e($site_title) ?>">
    <meta name="twitter:description" content="<?= isset($page_description) ? e($page_description) : e($site_description) ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #6c757d;
            --accent-color: #ffc107;
            --text-color: #333;
            --light-bg: #f8f9fa;
        }
        
        body {
            font-family: 'Noto Sans Bengali', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
        }
        
        /* Header Styles */
        .top-header {
            background-color: var(--primary-color);
            color: white;
            padding: 0.5rem 0;
            font-size: 0.9rem;
        }
        
        .main-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .site-logo {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .site-logo:hover {
            color: var(--primary-color);
        }
        
        .main-nav .nav-link {
            color: var(--text-color);
            font-weight: 500;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .main-nav .nav-link:hover,
        .main-nav .nav-link.active {
            color: var(--primary-color);
        }
        
        .main-nav .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background-color: var(--primary-color);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .main-nav .nav-link:hover::after,
        .main-nav .nav-link.active::after {
            width: 80%;
        }
        
        /* Breaking News Ticker */
        .breaking-news {
            overflow: hidden;
        }
        
        .breaking-news-ticker {
            overflow: hidden;
        }
        
        .ticker-content {
            display: flex;
            animation: ticker 30s linear infinite;
        }
        
        .ticker-item {
            white-space: nowrap;
            margin-right: 3rem;
        }
        
        @keyframes ticker {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        /* Search Box */
        .search-box {
            position: relative;
        }
        
        .search-box .form-control {
            border-radius: 25px;
            padding-right: 3rem;
        }
        
        .search-box .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
        }
        
        /* Featured Posts */
        .featured-card {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            min-height: 400px;
        }
        
        .featured-card .card-img-top {
            height: 100%;
            object-fit: cover;
        }
        
        .featured-card .card-img-overlay {
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
        }
        
        .featured-side-card {
            transition: transform 0.3s ease;
        }
        
        .featured-side-card:hover {
            transform: translateY(-5px);
        }
        
        /* News Cards */
        .news-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .news-card .card-img-top {
            height: 200px;
            object-fit: cover;
        }
        
        /* Category Badges */
        .category-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
        }
        
        .category-badge-sm {
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
        }
        
        /* Section Titles */
        .section-title {
            font-weight: 600;
            color: var(--text-color);
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--accent-color);
        }
        
        /* Sidebar Widgets */
        .sidebar-widget {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .widget-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .popular-post-item {
            padding: 1rem;
            border-radius: 10px;
            transition: background-color 0.3s ease;
        }
        
        .popular-post-item:hover {
            background-color: var(--light-bg);
        }
        
        .category-item {
            background: var(--light-bg);
            transition: all 0.3s ease;
            color: var(--text-color);
        }
        
        .category-item:hover {
            background: var(--primary-color);
            color: white;
            transform: translateX(5px);
        }
        
        /* Newsletter Widget */
        .newsletter-widget {
            background: linear-gradient(135deg, var(--primary-color), #20c997);
        }
        
        /* Ad Placeholder */
        .ad-placeholder {
            border: 2px dashed #dee2e6;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .site-logo {
                font-size: 1.5rem;
            }
            
            .main-nav .nav-link {
                padding: 0.75rem 1rem;
            }
            
            .featured-card {
                min-height: 300px;
            }
            
            .breaking-news {
                font-size: 0.9rem;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #e9ecef;
                --light-bg: #343a40;
            }
            
            body {
                background-color: #212529;
                color: var(--text-color);
            }
            
            .main-header {
                background: #343a40;
            }
            
            .sidebar-widget,
            .news-card {
                background: #343a40;
                color: var(--text-color);
            }
        }
    </style>
</head>
<body>
    <!-- Top Header -->
    <div class="top-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <span><?= date('l, F j, Y') ?></span>
                        <span class="ms-3">
                            <i class="fas fa-clock me-1"></i>
                            <span id="currentTime"></span>
                        </span>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Header -->
    <header class="main-header">
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-light">
                <a class="navbar-brand site-logo" href="<?= SITE_URL ?>">
                    <i class="fas fa-newspaper me-2"></i><?= e($site_title) ?>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="mainNav">
                    <ul class="navbar-nav main-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : '' ?>" 
                               href="<?= SITE_URL ?>">হোম</a>
                        </li>
                        
                        <?php foreach ($categories as $category): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="category.php?slug=<?= $category['slug'] ?>">
                                    <?= e($category['name_bn']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="about.php">আমাদের সম্পর্কে</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="contact.php">যোগাযোগ</a>
                        </li>
                    </ul>
                    
                    <!-- Search Box -->
                    <div class="search-box">
                        <form action="search.php" method="GET" class="d-flex">
                            <div class="position-relative">
                                <input type="text" name="q" class="form-control" placeholder="সংবাদ খুঁজুন..." 
                                       value="<?= isset($_GET['q']) ? e($_GET['q']) : '' ?>">
                                <button type="submit" class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </nav>
        </div>
    </header>
