<?php
/**
 * Load More Posts AJAX Handler
 * Bengali News CMS
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$response = ['success' => false, 'html' => '', 'hasMore' => false];

$page = (int)($_GET['page'] ?? 1);
$category_slug = $_GET['category'] ?? '';
$limit = 10;

try {
    if (!empty($category_slug)) {
        // Load posts by category
        $posts = getPostsByCategory($category_slug, $page, $limit);
        
        // Check if there are more posts
        $total_posts_stmt = $db->prepare("SELECT COUNT(*) as total FROM posts p 
                                         JOIN categories c ON p.category_id = c.id 
                                         WHERE p.status = 'published' AND c.slug = ?");
        $total_posts_stmt->execute([$category_slug]);
        $total_posts = $total_posts_stmt->fetch()['total'];
        $response['hasMore'] = ($page * $limit) < $total_posts;
        
    } else {
        // Load latest posts
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug, c.color as category_color,
                       u.full_name as author_name, u.profile_image as author_image
                FROM posts p 
                JOIN categories c ON p.category_id = c.id 
                JOIN users u ON p.author_id = u.id 
                WHERE p.status = 'published' 
                ORDER BY p.published_at DESC 
                LIMIT ? OFFSET ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$limit, $offset]);
        $posts = $stmt->fetchAll();
        
        // Check if there are more posts
        $total_posts_stmt = $db->prepare("SELECT COUNT(*) as total FROM posts WHERE status = 'published'");
        $total_posts_stmt->execute();
        $total_posts = $total_posts_stmt->fetch()['total'];
        $response['hasMore'] = ($page * $limit) < $total_posts;
    }
    
    if (!empty($posts)) {
        ob_start();
        
        foreach ($posts as $index => $post):
        ?>
            <div class="col-md-6 mb-4">
                <article class="news-card card h-100">
                    <?php if ($post['featured_image']): ?>
                        <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                             class="card-img-top" alt="<?= e($post['title_bn']) ?>">
                    <?php endif; ?>
                    <div class="card-body">
                        <span class="badge category-badge mb-2" 
                              style="background-color: <?= e($post['category_color']) ?>">
                            <?= e($post['category_name']) ?>
                        </span>
                        <h5 class="card-title">
                            <a href="post.php?slug=<?= $post['slug'] ?>" 
                               class="text-decoration-none">
                                <?= e($post['title_bn']) ?>
                            </a>
                        </h5>
                        <?php if ($post['excerpt']): ?>
                            <p class="card-text text-muted">
                                <?= e(truncateText($post['excerpt'], 100)) ?>
                            </p>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="post-meta d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i><?= e($post['author_name']) ?>
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i><?= timeAgo($post['published_at']) ?>
                            </small>
                        </div>
                    </div>
                </article>
            </div>
            
            <!-- Ad placeholder after every 4 posts -->
            <?php if (($index + 1) % 4 === 0): ?>
                <div class="col-12 mb-4">
                    <div class="ad-placeholder text-center py-4 bg-light rounded">
                        <p class="text-muted mb-0">Advertisement</p>
                    </div>
                </div>
            <?php endif; ?>
        <?php
        endforeach;
        
        $response['html'] = ob_get_clean();
        $response['success'] = true;
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?>
