<?php
/**
 * Get Media Details AJAX Handler
 * Bengali News CMS
 */

require_once '../../config/config.php';
requireLogin();

header('Content-Type: application/json');

$response = ['success' => false, 'html' => ''];

$media_id = $_GET['id'] ?? null;

if (!$media_id) {
    $response['message'] = 'Media ID required';
    echo json_encode($response);
    exit;
}

try {
    $stmt = $db->prepare("SELECT m.*, u.full_name as uploader_name 
                         FROM media m 
                         JOIN users u ON m.uploaded_by = u.id 
                         WHERE m.id = ?");
    $stmt->execute([$media_id]);
    $media = $stmt->fetch();
    
    if (!$media) {
        throw new Exception('Media not found');
    }
    
    $is_image = strpos($media['mime_type'], 'image/') === 0;
    
    $html = '<div class="media-viewer">';
    
    if ($is_image) {
        $html .= '<img src="' . UPLOAD_URL . $media['file_path'] . '" class="img-fluid mb-3" alt="' . e($media['alt_text']) . '">';
    } else {
        $html .= '<div class="text-center py-4">
                    <i class="fas fa-file fa-5x text-muted mb-3"></i>
                    <h5>' . e($media['original_name']) . '</h5>
                    <p class="text-muted">' . e($media['mime_type']) . '</p>
                    <a href="' . UPLOAD_URL . $media['file_path'] . '" class="btn btn-primary" target="_blank">
                        <i class="fas fa-download me-2"></i>Download
                    </a>
                  </div>';
    }
    
    $html .= '<div class="media-details">
                <table class="table table-sm">
                    <tr>
                        <td><strong>File Name:</strong></td>
                        <td>' . e($media['original_name']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>File Size:</strong></td>
                        <td>' . formatFileSize($media['file_size']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Type:</strong></td>
                        <td>' . e($media['mime_type']) . '</td>
                    </tr>';
    
    if ($media['alt_text']) {
        $html .= '<tr>
                    <td><strong>Alt Text:</strong></td>
                    <td>' . e($media['alt_text']) . '</td>
                  </tr>';
    }
    
    if ($media['caption']) {
        $html .= '<tr>
                    <td><strong>Caption:</strong></td>
                    <td>' . e($media['caption']) . '</td>
                  </tr>';
    }
    
    $html .= '<tr>
                <td><strong>Uploaded By:</strong></td>
                <td>' . e($media['uploader_name']) . '</td>
              </tr>
              <tr>
                <td><strong>Upload Date:</strong></td>
                <td>' . formatDate($media['created_at'], 'F j, Y g:i A') . '</td>
              </tr>
              <tr>
                <td><strong>URL:</strong></td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" value="' . UPLOAD_URL . $media['file_path'] . '" readonly>
                        <button type="button" class="btn btn-outline-secondary" onclick="copyToClipboard(\'' . UPLOAD_URL . $media['file_path'] . '\')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </td>
              </tr>
            </table>
          </div>';
    
    $html .= '</div>';
    
    $response['success'] = true;
    $response['html'] = $html;
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?>
