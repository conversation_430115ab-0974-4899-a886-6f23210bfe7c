<?php
/**
 * Single Post Page - Bengali News CMS
 */

require_once 'config/config.php';

$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Get post by slug
$post = getPostBySlug($slug);

if (!$post) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Record post view
recordPostView($post['id']);

// Get post tags
$post_tags = getPostTags($post['id']);

// Get related posts
$related_posts = getRelatedPosts($post['id'], $post['category_id'], 4);

// Set page meta data
$page_title = $post['meta_title'] ?: $post['title_bn'];
$page_description = $post['meta_description'] ?: truncateText(strip_tags($post['content']), 160);
$page_image = $post['featured_image'] ? UPLOAD_URL . $post['featured_image'] : null;

include 'includes/frontend_header.php';
?>

<div class="container my-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= SITE_URL ?>">হোম</a></li>
                    <li class="breadcrumb-item">
                        <a href="category.php?slug=<?= $post['category_slug'] ?>"><?= e($post['category_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page"><?= e(truncateText($post['title_bn'], 50)) ?></li>
                </ol>
            </nav>
            
            <!-- Post Content -->
            <article class="post-content">
                <!-- Post Header -->
                <header class="post-header mb-4">
                    <!-- Category Badge -->
                    <div class="mb-3">
                        <span class="badge category-badge" style="background-color: <?= e($post['category_color']) ?>">
                            <?= e($post['category_name']) ?>
                        </span>
                        <?php if ($post['is_featured']): ?>
                            <span class="badge bg-warning text-dark ms-2">ফিচার্ড</span>
                        <?php endif; ?>
                        <?php if ($post['is_breaking']): ?>
                            <span class="badge bg-danger ms-2">ব্রেকিং নিউজ</span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Post Title -->
                    <h1 class="post-title mb-3"><?= e($post['title_bn']) ?></h1>
                    
                    <!-- Post Meta -->
                    <div class="post-meta d-flex flex-wrap align-items-center mb-4">
                        <div class="author-info d-flex align-items-center me-4">
                            <?php if ($post['author_image']): ?>
                                <img src="<?= UPLOAD_URL . $post['author_image'] ?>" 
                                     class="rounded-circle me-2" width="40" height="40" alt="Author">
                            <?php else: ?>
                                <div class="author-avatar rounded-circle me-2 bg-primary text-white d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <?= strtoupper(substr($post['author_name'], 0, 1)) ?>
                                </div>
                            <?php endif; ?>
                            <div>
                                <div class="fw-bold"><?= e($post['author_name']) ?></div>
                                <?php if ($post['author_bio']): ?>
                                    <small class="text-muted"><?= e(truncateText($post['author_bio'], 50)) ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="post-date me-4">
                            <i class="fas fa-calendar-alt text-muted me-1"></i>
                            <span><?= formatDate($post['published_at'], 'F j, Y') ?></span>
                        </div>
                        
                        <div class="post-time me-4">
                            <i class="fas fa-clock text-muted me-1"></i>
                            <span><?= timeAgo($post['published_at']) ?></span>
                        </div>
                        
                        <div class="post-views">
                            <i class="fas fa-eye text-muted me-1"></i>
                            <span><?= number_format($post['view_count']) ?> ভিউ</span>
                        </div>
                    </div>
                    
                    <!-- Social Share Buttons -->
                    <div class="social-share mb-4">
                        <span class="me-3 fw-bold">শেয়ার করুন:</span>
                        <button class="btn btn-primary btn-sm me-2" 
                                onclick="shareOnSocial('facebook', '<?= SITE_URL ?>/post.php?slug=<?= $post['slug'] ?>', '<?= e($post['title_bn']) ?>')">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </button>
                        <button class="btn btn-info btn-sm me-2" 
                                onclick="shareOnSocial('twitter', '<?= SITE_URL ?>/post.php?slug=<?= $post['slug'] ?>', '<?= e($post['title_bn']) ?>')">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </button>
                        <button class="btn btn-success btn-sm me-2" 
                                onclick="shareOnSocial('whatsapp', '<?= SITE_URL ?>/post.php?slug=<?= $post['slug'] ?>', '<?= e($post['title_bn']) ?>')">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="copyToClipboard('<?= SITE_URL ?>/post.php?slug=<?= $post['slug'] ?>')">
                            <i class="fas fa-copy me-1"></i>Copy Link
                        </button>
                    </div>
                </header>
                
                <!-- Featured Image -->
                <?php if ($post['featured_image']): ?>
                    <div class="featured-image mb-4">
                        <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                             class="img-fluid rounded" alt="<?= e($post['title_bn']) ?>">
                    </div>
                <?php endif; ?>
                
                <!-- Post Excerpt -->
                <?php if ($post['excerpt']): ?>
                    <div class="post-excerpt bg-light p-4 rounded mb-4">
                        <h5 class="mb-3">সংক্ষেপে:</h5>
                        <p class="mb-0 fs-5"><?= e($post['excerpt']) ?></p>
                    </div>
                <?php endif; ?>
                
                <!-- Post Content -->
                <div class="post-body">
                    <?= $post['content'] ?>
                </div>
                
                <!-- Post Tags -->
                <?php if (!empty($post_tags)): ?>
                    <div class="post-tags mt-4 pt-4 border-top">
                        <h6 class="mb-3">ট্যাগসমূহ:</h6>
                        <?php foreach ($post_tags as $tag): ?>
                            <a href="tag.php?slug=<?= $tag['slug'] ?>" 
                               class="badge bg-light text-dark text-decoration-none me-2 mb-2">
                                #<?= e($tag['name_bn']) ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Author Bio -->
                <?php if ($post['author_bio']): ?>
                    <div class="author-bio mt-4 pt-4 border-top">
                        <div class="d-flex">
                            <?php if ($post['author_image']): ?>
                                <img src="<?= UPLOAD_URL . $post['author_image'] ?>" 
                                     class="rounded-circle me-3" width="80" height="80" alt="Author">
                            <?php else: ?>
                                <div class="author-avatar rounded-circle me-3 bg-primary text-white d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px; font-size: 2rem;">
                                    <?= strtoupper(substr($post['author_name'], 0, 1)) ?>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h5><?= e($post['author_name']) ?></h5>
                                <p class="text-muted"><?= e($post['author_bio']) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </article>
            
            <!-- Related Posts -->
            <?php if (!empty($related_posts)): ?>
                <section class="related-posts mt-5">
                    <h3 class="section-title mb-4">
                        <i class="fas fa-newspaper text-primary me-2"></i>সম্পর্কিত সংবাদ
                    </h3>
                    
                    <div class="row">
                        <?php foreach ($related_posts as $related): ?>
                            <div class="col-md-6 mb-4">
                                <div class="card news-card h-100">
                                    <?php if ($related['featured_image']): ?>
                                        <img src="<?= UPLOAD_URL . $related['featured_image'] ?>" 
                                             class="card-img-top" alt="<?= e($related['title_bn']) ?>">
                                    <?php endif; ?>
                                    <div class="card-body">
                                        <span class="badge category-badge mb-2" 
                                              style="background-color: <?= e($related['category_color']) ?>">
                                            <?= e($related['category_name']) ?>
                                        </span>
                                        <h6 class="card-title">
                                            <a href="post.php?slug=<?= $related['slug'] ?>" 
                                               class="text-decoration-none">
                                                <?= e(truncateText($related['title_bn'], 80)) ?>
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i><?= timeAgo($related['published_at']) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Popular Posts -->
            <?php 
            $popular_posts = getPopularPosts(5);
            if (!empty($popular_posts)): 
            ?>
                <div class="sidebar-widget mb-4">
                    <h4 class="widget-title">
                        <i class="fas fa-fire text-danger me-2"></i>জনপ্রিয় সংবাদ
                    </h4>
                    <div class="popular-posts">
                        <?php foreach ($popular_posts as $index => $popular): ?>
                            <div class="popular-post-item d-flex mb-3">
                                <div class="popular-number me-3">
                                    <span class="badge bg-primary"><?= $index + 1 ?></span>
                                </div>
                                <?php if ($popular['featured_image']): ?>
                                    <div class="popular-image me-3">
                                        <img src="<?= UPLOAD_URL . $popular['featured_image'] ?>" 
                                             class="rounded" width="80" height="60" 
                                             style="object-fit: cover;" alt="<?= e($popular['title_bn']) ?>">
                                    </div>
                                <?php endif; ?>
                                <div class="popular-content flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="post.php?slug=<?= $popular['slug'] ?>" 
                                           class="text-decoration-none">
                                            <?= e(truncateText($popular['title_bn'], 60)) ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i><?= number_format($popular['view_count']) ?> ভিউ
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Latest Posts -->
            <?php 
            $latest_posts = getLatestPosts(5);
            if (!empty($latest_posts)): 
            ?>
                <div class="sidebar-widget mb-4">
                    <h4 class="widget-title">
                        <i class="fas fa-clock text-info me-2"></i>সর্বশেষ সংবাদ
                    </h4>
                    <div class="latest-posts">
                        <?php foreach ($latest_posts as $latest): ?>
                            <?php if ($latest['id'] != $post['id']): // Don't show current post ?>
                                <div class="latest-post-item mb-3">
                                    <div class="d-flex">
                                        <?php if ($latest['featured_image']): ?>
                                            <div class="latest-image me-3">
                                                <img src="<?= UPLOAD_URL . $latest['featured_image'] ?>" 
                                                     class="rounded" width="80" height="60" 
                                                     style="object-fit: cover;" alt="<?= e($latest['title_bn']) ?>">
                                            </div>
                                        <?php endif; ?>
                                        <div class="latest-content flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="post.php?slug=<?= $latest['slug'] ?>" 
                                                   class="text-decoration-none">
                                                    <?= e(truncateText($latest['title_bn'], 60)) ?>
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i><?= timeAgo($latest['published_at']) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Ad Placeholder -->
            <div class="sidebar-widget mb-4">
                <div class="ad-placeholder text-center py-5 bg-light rounded">
                    <p class="text-muted mb-0">Advertisement</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$custom_scripts = "
<script>
    // Copy to clipboard function
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            alert('লিংক কপি হয়েছে!');
        });
    }
    
    // Social sharing functions
    function shareOnSocial(platform, url, title) {
        let shareUrl = '';
        
        switch (platform) {
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=\${encodeURIComponent(url)}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?url=\${encodeURIComponent(url)}&text=\${encodeURIComponent(title)}`;
                break;
            case 'whatsapp':
                shareUrl = `https://wa.me/?text=\${encodeURIComponent(title + ' ' + url)}`;
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    }
    
    // Reading progress indicator
    function updateReadingProgress() {
        const article = document.querySelector('.post-content');
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        // You can add a progress bar here
        console.log('Reading progress:', scrollPercent + '%');
    }
    
    window.addEventListener('scroll', updateReadingProgress);
</script>
";

include 'includes/frontend_footer.php';
?>
