<?php
/**
 * Pages Management
 * Bengali News CMS
 */

require_once '../config/config.php';
requireLogin();

if (!hasPermission('editor')) {
    showMessage('You do not have permission to manage pages', 'error');
    redirect(ADMIN_URL . '/dashboard.php');
}

$action = $_GET['action'] ?? 'list';
$page_id = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token';
    } else {
        if ($action === 'add' || $action === 'edit') {
            $title = cleanInput($_POST['title']);
            $title_bn = cleanInput($_POST['title_bn']);
            $content = $_POST['content']; // Don't clean HTML content
            $meta_title = cleanInput($_POST['meta_title']);
            $meta_description = cleanInput($_POST['meta_description']);
            $status = cleanInput($_POST['status']);
            $sort_order = (int)$_POST['sort_order'];
            
            // Generate slug
            $slug = generateSlug($title_bn ?: $title);
            
            if ($action === 'add') {
                // Check if slug already exists
                $stmt = $db->prepare("SELECT id FROM pages WHERE slug = ?");
                $stmt->execute([$slug]);
                if ($stmt->fetch()) {
                    $slug .= '-' . time();
                }
                
                $sql = "INSERT INTO pages (title, title_bn, slug, content, meta_title, meta_description, status, sort_order) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                
                $stmt = $db->prepare($sql);
                $result = $stmt->execute([$title, $title_bn, $slug, $content, $meta_title, $meta_description, $status, $sort_order]);
                
                if ($result) {
                    $success = 'Page created successfully';
                } else {
                    $error = 'Failed to create page';
                }
                
            } elseif ($action === 'edit' && $page_id) {
                // Check if slug already exists (excluding current page)
                $stmt = $db->prepare("SELECT id FROM pages WHERE slug = ? AND id != ?");
                $stmt->execute([$slug, $page_id]);
                if ($stmt->fetch()) {
                    $slug .= '-' . time();
                }
                
                $sql = "UPDATE pages SET title = ?, title_bn = ?, slug = ?, content = ?, meta_title = ?, 
                                        meta_description = ?, status = ?, sort_order = ?, updated_at = NOW()
                        WHERE id = ?";
                
                $stmt = $db->prepare($sql);
                $result = $stmt->execute([$title, $title_bn, $slug, $content, $meta_title, $meta_description, $status, $sort_order, $page_id]);
                
                if ($result) {
                    $success = 'Page updated successfully';
                } else {
                    $error = 'Failed to update page';
                }
            }
        } elseif ($action === 'delete' && $page_id) {
            $stmt = $db->prepare("DELETE FROM pages WHERE id = ?");
            if ($stmt->execute([$page_id])) {
                $success = 'Page deleted successfully';
            } else {
                $error = 'Failed to delete page';
            }
        }
    }
}

// Get page data for edit
$page_data = null;
if ($action === 'edit' && $page_id) {
    $stmt = $db->prepare("SELECT * FROM pages WHERE id = ?");
    $stmt->execute([$page_id]);
    $page_data = $stmt->fetch();
}

$page_title = ($action === 'add') ? 'Add Page' : (($action === 'edit') ? 'Edit Page' : 'Pages');
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($action === 'list'): ?>
        <!-- Pages List -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Pages</h1>
                    <a href="pages.php?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Page
                    </a>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <?php
                        // Get all pages
                        $stmt = $db->prepare("SELECT * FROM pages ORDER BY sort_order ASC, title_bn ASC");
                        $stmt->execute();
                        $pages = $stmt->fetchAll();
                        ?>
                        
                        <?php if (empty($pages)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h5>No pages found</h5>
                                <p class="text-muted">Start by creating your first page</p>
                                <a href="pages.php?action=add" class="btn btn-primary">Add New Page</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Order</th>
                                            <th>Title</th>
                                            <th>Slug</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pages as $page): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-secondary"><?= $page['sort_order'] ?></span>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div class="fw-bold"><?= e($page['title_bn']) ?></div>
                                                        <?php if ($page['title']): ?>
                                                            <small class="text-muted"><?= e($page['title']) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <code><?= e($page['slug']) ?></code>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $page['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                        <?= ucfirst($page['status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= timeAgo($page['created_at']) ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="pages.php?action=edit&id=<?= $page['id'] ?>" 
                                                           class="btn btn-outline-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <?php if ($page['status'] === 'active'): ?>
                                                            <a href="<?= SITE_URL ?>/page.php?slug=<?= $page['slug'] ?>" 
                                                               class="btn btn-outline-success" title="View" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="pages.php?action=delete&id=<?= $page['id'] ?>" 
                                                           class="btn btn-outline-danger" title="Delete"
                                                           onclick="return confirmDelete('Are you sure you want to delete this page?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Add/Edit Page Form -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><?= $action === 'add' ? 'Add New Page' : 'Edit Page' ?></h1>
                    <a href="pages.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Pages
                    </a>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= e($error) ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?= e($success) ?></div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Page Content</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="pageForm">
                                    <?= getCSRFTokenInput() ?>
                                    
                                    <div class="mb-3">
                                        <label for="title_bn" class="form-label">Title (Bengali) *</label>
                                        <input type="text" class="form-control" id="title_bn" name="title_bn" 
                                               value="<?= e($page_data['title_bn'] ?? '') ?>" required
                                               onkeyup="generateSlug(this.value, 'slug_preview')">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title (English)</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="<?= e($page_data['title'] ?? '') ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">URL Slug</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?= SITE_URL ?>/page/</span>
                                            <input type="text" class="form-control" id="slug_preview" readonly
                                                   value="<?= e($page_data['slug'] ?? '') ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="content" class="form-label">Content *</label>
                                        <textarea class="form-control summernote" id="content" name="content" required><?= $page_data['content'] ?? '' ?></textarea>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            <?= $action === 'add' ? 'Create Page' : 'Update Page' ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- Page Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Page Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status" form="pageForm">
                                        <option value="active" <?= ($page_data['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= ($page_data['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           value="<?= e($page_data['sort_order'] ?? 0) ?>" min="0" form="pageForm">
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- SEO Settings -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">SEO Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="meta_title" class="form-label">Meta Title</label>
                                    <input type="text" class="form-control" id="meta_title" name="meta_title" 
                                           value="<?= e($page_data['meta_title'] ?? '') ?>"
                                           maxlength="60" onkeyup="updateCharCount('meta_title', 'meta_title_count', 60)" form="pageForm">
                                    <div class="form-text" id="meta_title_count">60 characters remaining</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea class="form-control" id="meta_description" name="meta_description" rows="3"
                                              maxlength="160" onkeyup="updateCharCount('meta_description', 'meta_desc_count', 160)" form="pageForm"><?= e($page_data['meta_description'] ?? '') ?></textarea>
                                    <div class="form-text" id="meta_desc_count">160 characters remaining</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
$custom_scripts = "
<script>
    // Initialize character counters
    updateCharCount('meta_title', 'meta_title_count', 60);
    updateCharCount('meta_description', 'meta_desc_count', 160);
</script>
";

include 'includes/footer.php';
?>
