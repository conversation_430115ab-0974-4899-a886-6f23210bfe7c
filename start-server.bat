@echo off
echo 🚀 Bengali News CMS - Quick Start
echo ==================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Docker found! Starting with Docker...
    
    REM Create necessary directories
    if not exist "uploads\images" mkdir uploads\images
    if not exist "uploads\documents" mkdir uploads\documents
    if not exist "uploads\categories" mkdir uploads\categories
    if not exist "cache" mkdir cache
    if not exist "config" mkdir config
    
    REM Start Docker containers
    echo 🐳 Starting Docker containers...
    docker-compose up -d
    
    echo.
    echo 🎉 Server is starting up!
    echo 📱 Your CMS will be available at: http://localhost:8080
    echo 🗄️  phpMyAdmin will be available at: http://localhost:8081
    echo.
    echo Database credentials:
    echo   Host: localhost (or db from within containers)
    echo   Database: bengali_news
    echo   Username: root
    echo   Password: password
    echo.
    echo ⏳ Please wait 30-60 seconds for services to fully start...
    echo 🔧 Then visit http://localhost:8080/install.php to complete setup
    
    pause
    goto :end
)

REM Check if PHP is installed
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ PHP found! Starting with built-in server...
    
    REM Create necessary directories
    if not exist "uploads\images" mkdir uploads\images
    if not exist "uploads\documents" mkdir uploads\documents
    if not exist "uploads\categories" mkdir uploads\categories
    if not exist "cache" mkdir cache
    if not exist "config" mkdir config
    
    REM Start PHP built-in server
    echo 🚀 Starting PHP development server...
    echo 📱 Your CMS will be available at: http://localhost:8000
    echo.
    echo ⚠️  Note: You'll need to set up MySQL separately
    echo 🔧 Visit http://localhost:8000/install.php to complete setup
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    
    php -S localhost:8000
    goto :end
)

echo ❌ Neither Docker nor PHP found!
echo.
echo Please install one of the following:
echo.
echo 🐳 Docker (Recommended):
echo    - Download Docker Desktop for Windows
echo.
echo 🐘 PHP + MySQL:
echo    - XAMPP: https://www.apachefriends.org/
echo    - WAMP: http://www.wampserver.com/
echo.
echo 📚 See README.md for detailed installation instructions

:end
pause
