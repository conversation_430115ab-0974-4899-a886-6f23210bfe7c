<?php
/**
 * General Functions
 * Bengali News CMS
 */

/**
 * Get latest posts
 */
function getLatestPosts($limit = 15) {
    global $db;
    
    $cache_key = "latest_posts_$limit";
    $cached = getCache($cache_key);
    if ($cached) {
        return $cached;
    }
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug, c.color as category_color,
                   u.full_name as author_name, u.profile_image as author_image
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            JOIN users u ON p.author_id = u.id 
            WHERE p.status = 'published' 
            ORDER BY p.published_at DESC 
            LIMIT ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$limit]);
    $posts = $stmt->fetchAll();
    
    setCache($cache_key, $posts, 1800); // Cache for 30 minutes
    return $posts;
}

/**
 * Get popular posts based on views in last 7 days
 */
function getPopularPosts($limit = 7, $days = 7) {
    global $db;
    
    $cache_key = "popular_posts_{$limit}_{$days}";
    $cached = getCache($cache_key);
    if ($cached) {
        return $cached;
    }
    
    $date_from = date('Y-m-d H:i:s', strtotime("-$days days"));
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug, c.color as category_color,
                   u.full_name as author_name, u.profile_image as author_image,
                   COUNT(pv.id) as recent_views
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            JOIN users u ON p.author_id = u.id 
            LEFT JOIN post_views pv ON p.id = pv.post_id AND pv.viewed_at >= ?
            WHERE p.status = 'published' 
            GROUP BY p.id
            ORDER BY recent_views DESC, p.view_count DESC
            LIMIT ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$date_from, $limit]);
    $posts = $stmt->fetchAll();
    
    setCache($cache_key, $posts, 3600); // Cache for 1 hour
    return $posts;
}

/**
 * Get posts by category
 */
function getPostsByCategory($category_slug, $page = 1, $limit = 10) {
    global $db;
    
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug, c.color as category_color,
                   u.full_name as author_name, u.profile_image as author_image
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            JOIN users u ON p.author_id = u.id 
            WHERE p.status = 'published' AND c.slug = ?
            ORDER BY p.published_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$category_slug, $limit, $offset]);
    return $stmt->fetchAll();
}

/**
 * Get single post by slug
 */
function getPostBySlug($slug) {
    global $db;
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug, c.color as category_color,
                   u.full_name as author_name, u.profile_image as author_image, u.bio as author_bio
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            JOIN users u ON p.author_id = u.id 
            WHERE p.slug = ? AND p.status = 'published'";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

/**
 * Get post tags
 */
function getPostTags($post_id) {
    global $db;
    
    $sql = "SELECT t.* FROM tags t 
            JOIN post_tags pt ON t.id = pt.tag_id 
            WHERE pt.post_id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$post_id]);
    return $stmt->fetchAll();
}

/**
 * Get all categories
 */
function getCategories($active_only = true) {
    global $db;
    
    $cache_key = "categories_" . ($active_only ? 'active' : 'all');
    $cached = getCache($cache_key);
    if ($cached) {
        return $cached;
    }
    
    $sql = "SELECT * FROM categories";
    if ($active_only) {
        $sql .= " WHERE status = 'active'";
    }
    $sql .= " ORDER BY sort_order ASC, name_bn ASC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    setCache($cache_key, $categories, 3600); // Cache for 1 hour
    return $categories;
}

/**
 * Get category by slug
 */
function getCategoryBySlug($slug) {
    global $db;
    
    $stmt = $db->prepare("SELECT * FROM categories WHERE slug = ? AND status = 'active'");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

/**
 * Get featured posts
 */
function getFeaturedPosts($limit = 5) {
    global $db;
    
    $cache_key = "featured_posts_$limit";
    $cached = getCache($cache_key);
    if ($cached) {
        return $cached;
    }
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug, c.color as category_color,
                   u.full_name as author_name
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            JOIN users u ON p.author_id = u.id 
            WHERE p.status = 'published' AND p.is_featured = 1
            ORDER BY p.published_at DESC 
            LIMIT ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$limit]);
    $posts = $stmt->fetchAll();
    
    setCache($cache_key, $posts, 1800); // Cache for 30 minutes
    return $posts;
}

/**
 * Get breaking news
 */
function getBreakingNews($limit = 3) {
    global $db;
    
    $cache_key = "breaking_news_$limit";
    $cached = getCache($cache_key);
    if ($cached) {
        return $cached;
    }
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            WHERE p.status = 'published' AND p.is_breaking = 1
            ORDER BY p.published_at DESC 
            LIMIT ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$limit]);
    $posts = $stmt->fetchAll();
    
    setCache($cache_key, $posts, 600); // Cache for 10 minutes
    return $posts;
}

/**
 * Record post view
 */
function recordPostView($post_id) {
    global $db;
    
    $ip_address = getClientIP();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Check if this IP has viewed this post in the last hour
    $sql = "SELECT id FROM post_views 
            WHERE post_id = ? AND ip_address = ? AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$post_id, $ip_address]);
    
    if (!$stmt->fetch()) {
        // Record new view
        $sql = "INSERT INTO post_views (post_id, ip_address, user_agent) VALUES (?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$post_id, $ip_address, $user_agent]);
        
        // Update post view count
        $sql = "UPDATE posts SET view_count = view_count + 1 WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$post_id]);
        
        // Clear popular posts cache
        clearCacheByPattern('popular_posts_');
    }
}

/**
 * Search posts
 */
function searchPosts($query, $page = 1, $limit = 10) {
    global $db;
    
    $offset = ($page - 1) * $limit;
    $search_term = "%$query%";
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug,
                   u.full_name as author_name
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            JOIN users u ON p.author_id = u.id 
            WHERE p.status = 'published' 
            AND (p.title LIKE ? OR p.title_bn LIKE ? OR p.content LIKE ? OR p.excerpt LIKE ?)
            ORDER BY p.published_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$search_term, $search_term, $search_term, $search_term, $limit, $offset]);
    return $stmt->fetchAll();
}

/**
 * Get total search results count
 */
function getSearchResultsCount($query) {
    global $db;
    
    $search_term = "%$query%";
    
    $sql = "SELECT COUNT(*) as total FROM posts p 
            WHERE p.status = 'published' 
            AND (p.title LIKE ? OR p.title_bn LIKE ? OR p.content LIKE ? OR p.excerpt LIKE ?)";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$search_term, $search_term, $search_term, $search_term]);
    $result = $stmt->fetch();
    return $result['total'];
}

/**
 * Get related posts
 */
function getRelatedPosts($post_id, $category_id, $limit = 4) {
    global $db;
    
    $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug
            FROM posts p 
            JOIN categories c ON p.category_id = c.id 
            WHERE p.status = 'published' AND p.category_id = ? AND p.id != ?
            ORDER BY p.published_at DESC 
            LIMIT ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$category_id, $post_id, $limit]);
    return $stmt->fetchAll();
}

/**
 * Cache functions
 */
function getCache($key) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    global $db;
    
    $stmt = $db->prepare("SELECT cache_value FROM cache WHERE cache_key = ? AND expires_at > NOW()");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    
    if ($result) {
        return json_decode($result['cache_value'], true);
    }
    
    return false;
}

function setCache($key, $value, $duration = null) {
    if (!CACHE_ENABLED) {
        return false;
    }
    
    global $db;
    
    if ($duration === null) {
        $duration = CACHE_DURATION;
    }
    
    $expires_at = date('Y-m-d H:i:s', time() + $duration);
    $cache_value = json_encode($value);
    
    $stmt = $db->prepare("INSERT INTO cache (cache_key, cache_value, expires_at) 
                         VALUES (?, ?, ?) 
                         ON DUPLICATE KEY UPDATE cache_value = ?, expires_at = ?");
    return $stmt->execute([$key, $cache_value, $expires_at, $cache_value, $expires_at]);
}

function clearCache($key) {
    global $db;
    
    $stmt = $db->prepare("DELETE FROM cache WHERE cache_key = ?");
    return $stmt->execute([$key]);
}

function clearCacheByPattern($pattern) {
    global $db;
    
    $stmt = $db->prepare("DELETE FROM cache WHERE cache_key LIKE ?");
    return $stmt->execute([$pattern . '%']);
}

function clearAllCache() {
    global $db;
    
    $stmt = $db->prepare("DELETE FROM cache");
    return $stmt->execute();
}

/**
 * Get page by slug
 */
function getPageBySlug($slug) {
    global $db;
    
    $stmt = $db->prepare("SELECT * FROM pages WHERE slug = ? AND status = 'active'");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

/**
 * Get all pages
 */
function getPages() {
    global $db;
    
    $stmt = $db->prepare("SELECT * FROM pages WHERE status = 'active' ORDER BY sort_order ASC, title ASC");
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Generate breadcrumb
 */
function generateBreadcrumb($items) {
    $breadcrumb = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    
    foreach ($items as $index => $item) {
        if ($index === count($items) - 1) {
            $breadcrumb .= '<li class="breadcrumb-item active" aria-current="page">' . e($item['title']) . '</li>';
        } else {
            $breadcrumb .= '<li class="breadcrumb-item"><a href="' . e($item['url']) . '">' . e($item['title']) . '</a></li>';
        }
    }
    
    $breadcrumb .= '</ol></nav>';
    return $breadcrumb;
}
?>
