<?php
/**
 * Cron Job: Cache Cleanup
 * Bengali News CMS
 * 
 * Run this script daily to clean up expired cache entries
 * Add to crontab: 0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_cache.php
 */

// Prevent direct web access
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

require_once dirname(__DIR__) . '/config/config.php';

echo "Starting cache cleanup...\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";

try {
    // Clean up expired cache entries
    $stmt = $db->prepare("DELETE FROM cache WHERE expires_at < NOW()");
    $stmt->execute();
    $deleted_cache = $stmt->rowCount();
    
    echo "✓ Cleaned up $deleted_cache expired cache entries\n";
    
    // Clean up old security logs (older than 90 days)
    if ($db->query("SHOW TABLES LIKE 'security_logs'")->rowCount() > 0) {
        $cleanup_date = date('Y-m-d H:i:s', strtotime('-90 days'));
        $stmt = $db->prepare("DELETE FROM security_logs WHERE created_at < ?");
        $stmt->execute([$cleanup_date]);
        $deleted_logs = $stmt->rowCount();
        
        echo "✓ Cleaned up $deleted_logs old security log entries\n";
    }
    
    // Clean up old login attempts (older than 7 days)
    if ($db->query("SHOW TABLES LIKE 'login_attempts'")->rowCount() > 0) {
        $cleanup_date = date('Y-m-d H:i:s', strtotime('-7 days'));
        $stmt = $db->prepare("DELETE FROM login_attempts WHERE attempted_at < ?");
        $stmt->execute([$cleanup_date]);
        $deleted_attempts = $stmt->rowCount();
        
        echo "✓ Cleaned up $deleted_attempts old login attempt records\n";
    }
    
    // Optimize database tables
    $tables = ['posts', 'post_views', 'cache', 'security_logs', 'login_attempts', 'popular_posts_ranking'];
    
    foreach ($tables as $table) {
        try {
            $db->exec("OPTIMIZE TABLE $table");
            echo "✓ Optimized table: $table\n";
        } catch (Exception $e) {
            echo "⚠ Warning: Could not optimize table $table: " . $e->getMessage() . "\n";
        }
    }
    
    // Calculate cache statistics
    $stmt = $db->prepare("SELECT COUNT(*) as total_entries, 
                                 SUM(LENGTH(cache_value)) as total_size 
                          FROM cache");
    $stmt->execute();
    $cache_stats = $stmt->fetch();
    
    echo "Cache statistics:\n";
    echo "- Total entries: " . number_format($cache_stats['total_entries']) . "\n";
    echo "- Total size: " . formatFileSize($cache_stats['total_size']) . "\n";
    
    // Log the cleanup
    logSecurityEvent('cron_cache_cleanup', "Cleaned up $deleted_cache cache entries");
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    error_log('Cache cleanup cron job error: ' . $e->getMessage());
    exit(1);
}

echo "Cache cleanup completed successfully\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";
echo "---\n";
?>
