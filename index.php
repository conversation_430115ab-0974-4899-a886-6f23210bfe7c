<?php
/**
 * Homepage - Bengali News CMS
 */

require_once 'config/config.php';

// Get latest posts
$latest_posts = getLatestPosts(15);

// Get popular posts
$popular_posts = getPopularPosts(7);

// Get featured posts
$featured_posts = getFeaturedPosts(5);

// Get breaking news
$breaking_news = getBreakingNews(3);

// Get categories
$categories = getCategories();

$page_title = getSetting('site_title', 'Bengali News CMS');
$page_description = getSetting('site_description', 'Latest Bengali news and updates');

include 'includes/frontend_header.php';
?>

<!-- Breaking News Ticker -->
<?php if (!empty($breaking_news)): ?>
<div class="breaking-news bg-danger text-white py-2">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-auto">
                <span class="badge bg-light text-danger fw-bold">
                    <i class="fas fa-bolt me-1"></i>ব্রেকিং নিউজ
                </span>
            </div>
            <div class="col">
                <div class="breaking-news-ticker">
                    <div class="ticker-content">
                        <?php foreach ($breaking_news as $news): ?>
                            <span class="ticker-item">
                                <a href="post.php?slug=<?= $news['slug'] ?>" class="text-white text-decoration-none">
                                    <?= e($news['title_bn']) ?>
                                </a>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="container my-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Featured Posts Section -->
            <?php if (!empty($featured_posts)): ?>
            <section class="featured-posts mb-5">
                <h2 class="section-title mb-4">
                    <i class="fas fa-star text-warning me-2"></i>ফিচার্ড নিউজ
                </h2>
                
                <div class="row">
                    <!-- Main Featured Post -->
                    <div class="col-md-8 mb-4">
                        <?php $main_featured = $featured_posts[0]; ?>
                        <div class="card featured-card h-100">
                            <?php if ($main_featured['featured_image']): ?>
                                <img src="<?= UPLOAD_URL . $main_featured['featured_image'] ?>" 
                                     class="card-img-top" alt="<?= e($main_featured['title_bn']) ?>">
                            <?php endif; ?>
                            <div class="card-img-overlay d-flex flex-column justify-content-end">
                                <div class="featured-content">
                                    <span class="badge category-badge mb-2" 
                                          style="background-color: <?= e($main_featured['category_color']) ?>">
                                        <?= e($main_featured['category_name']) ?>
                                    </span>
                                    <h3 class="card-title text-white">
                                        <a href="post.php?slug=<?= $main_featured['slug'] ?>" 
                                           class="text-white text-decoration-none">
                                            <?= e($main_featured['title_bn']) ?>
                                        </a>
                                    </h3>
                                    <p class="card-text text-white-50">
                                        <?= e(truncateText($main_featured['excerpt'], 120)) ?>
                                    </p>
                                    <div class="post-meta text-white-50">
                                        <small>
                                            <i class="fas fa-user me-1"></i><?= e($main_featured['author_name']) ?>
                                            <i class="fas fa-clock ms-3 me-1"></i><?= timeAgo($main_featured['published_at']) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Side Featured Posts -->
                    <div class="col-md-4">
                        <?php for ($i = 1; $i < min(4, count($featured_posts)); $i++): ?>
                            <?php $post = $featured_posts[$i]; ?>
                            <div class="card mb-3 featured-side-card">
                                <div class="row g-0">
                                    <?php if ($post['featured_image']): ?>
                                        <div class="col-4">
                                            <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                                                 class="img-fluid rounded-start h-100" 
                                                 style="object-fit: cover;" alt="<?= e($post['title_bn']) ?>">
                                        </div>
                                    <?php endif; ?>
                                    <div class="<?= $post['featured_image'] ? 'col-8' : 'col-12' ?>">
                                        <div class="card-body p-3">
                                            <span class="badge category-badge-sm mb-1" 
                                                  style="background-color: <?= e($post['category_color']) ?>">
                                                <?= e($post['category_name']) ?>
                                            </span>
                                            <h6 class="card-title">
                                                <a href="post.php?slug=<?= $post['slug'] ?>" 
                                                   class="text-decoration-none">
                                                    <?= e(truncateText($post['title_bn'], 60)) ?>
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i><?= timeAgo($post['published_at']) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </section>
            <?php endif; ?>
            
            <!-- Latest News Section -->
            <section class="latest-news">
                <h2 class="section-title mb-4">
                    <i class="fas fa-newspaper text-primary me-2"></i>সর্বশেষ সংবাদ
                </h2>
                
                <div class="row">
                    <?php foreach ($latest_posts as $index => $post): ?>
                        <div class="col-md-6 mb-4">
                            <article class="news-card card h-100">
                                <?php if ($post['featured_image']): ?>
                                    <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                                         class="card-img-top" alt="<?= e($post['title_bn']) ?>">
                                <?php endif; ?>
                                <div class="card-body">
                                    <span class="badge category-badge mb-2" 
                                          style="background-color: <?= e($post['category_color']) ?>">
                                        <?= e($post['category_name']) ?>
                                    </span>
                                    <h5 class="card-title">
                                        <a href="post.php?slug=<?= $post['slug'] ?>" 
                                           class="text-decoration-none">
                                            <?= e($post['title_bn']) ?>
                                        </a>
                                    </h5>
                                    <?php if ($post['excerpt']): ?>
                                        <p class="card-text text-muted">
                                            <?= e(truncateText($post['excerpt'], 100)) ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="post-meta d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i><?= e($post['author_name']) ?>
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i><?= timeAgo($post['published_at']) ?>
                                        </small>
                                    </div>
                                </div>
                            </article>
                        </div>
                        
                        <!-- Ad placeholder after every 4 posts -->
                        <?php if (($index + 1) % 4 === 0): ?>
                            <div class="col-12 mb-4">
                                <div class="ad-placeholder text-center py-4 bg-light rounded">
                                    <p class="text-muted mb-0">Advertisement</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
                
                <!-- Load More Button -->
                <div class="text-center mt-4">
                    <button class="btn btn-outline-primary btn-lg" id="loadMoreBtn">
                        <i class="fas fa-plus me-2"></i>আরো সংবাদ দেখুন
                    </button>
                </div>
            </section>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Popular Posts -->
            <?php if (!empty($popular_posts)): ?>
            <div class="sidebar-widget mb-4">
                <h4 class="widget-title">
                    <i class="fas fa-fire text-danger me-2"></i>জনপ্রিয় সংবাদ
                </h4>
                <div class="popular-posts">
                    <?php foreach ($popular_posts as $index => $post): ?>
                        <div class="popular-post-item d-flex mb-3">
                            <div class="popular-number">
                                <span class="badge bg-primary"><?= $index + 1 ?></span>
                            </div>
                            <?php if ($post['featured_image']): ?>
                                <div class="popular-image me-3">
                                    <img src="<?= UPLOAD_URL . $post['featured_image'] ?>" 
                                         class="rounded" width="80" height="60" 
                                         style="object-fit: cover;" alt="<?= e($post['title_bn']) ?>">
                                </div>
                            <?php endif; ?>
                            <div class="popular-content flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="post.php?slug=<?= $post['slug'] ?>" 
                                       class="text-decoration-none">
                                        <?= e(truncateText($post['title_bn'], 70)) ?>
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-eye me-1"></i><?= number_format($post['view_count']) ?> ভিউ
                                    <i class="fas fa-clock ms-2 me-1"></i><?= timeAgo($post['published_at']) ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Categories -->
            <div class="sidebar-widget mb-4">
                <h4 class="widget-title">
                    <i class="fas fa-tags text-info me-2"></i>বিভাগসমূহ
                </h4>
                <div class="categories-list">
                    <?php foreach ($categories as $category): ?>
                        <a href="category.php?slug=<?= $category['slug'] ?>" 
                           class="category-item d-flex align-items-center justify-content-between text-decoration-none mb-2 p-3 rounded">
                            <div class="d-flex align-items-center">
                                <?php if ($category['icon']): ?>
                                    <img src="<?= UPLOAD_URL . $category['icon'] ?>" 
                                         width="24" height="24" class="me-2" alt="<?= e($category['name_bn']) ?>">
                                <?php else: ?>
                                    <div class="category-color-box me-2" 
                                         style="width: 24px; height: 24px; background-color: <?= e($category['color']) ?>; border-radius: 4px;"></div>
                                <?php endif; ?>
                                <span class="fw-medium"><?= e($category['name_bn']) ?></span>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Newsletter Signup -->
            <div class="sidebar-widget mb-4">
                <div class="newsletter-widget bg-primary text-white p-4 rounded">
                    <h4 class="widget-title text-white">
                        <i class="fas fa-envelope me-2"></i>নিউজলেটার
                    </h4>
                    <p class="mb-3">সর্বশেষ সংবাদ পেতে আমাদের নিউজলেটার সাবস্ক্রাইব করুন</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="আপনার ইমেইল ঠিকানা" required>
                            <button class="btn btn-light" type="submit">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="sidebar-widget">
                <h4 class="widget-title">
                    <i class="fas fa-share-alt text-success me-2"></i>সোশ্যাল মিডিয়া
                </h4>
                <div class="social-links d-flex gap-2">
                    <a href="#" class="btn btn-primary btn-sm flex-fill">
                        <i class="fab fa-facebook-f"></i> Facebook
                    </a>
                    <a href="#" class="btn btn-info btn-sm flex-fill">
                        <i class="fab fa-twitter"></i> Twitter
                    </a>
                    <a href="#" class="btn btn-danger btn-sm flex-fill">
                        <i class="fab fa-youtube"></i> YouTube
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/frontend_footer.php'; ?>
