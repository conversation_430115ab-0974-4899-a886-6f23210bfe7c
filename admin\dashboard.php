<?php
/**
 * Admin Dashboard
 * Bengali News CMS
 */

require_once '../config/config.php';
requireLogin();

// Get dashboard statistics
$stats = [];

// Total posts
$stmt = $db->prepare("SELECT COUNT(*) as total FROM posts");
$stmt->execute();
$stats['total_posts'] = $stmt->fetch()['total'];

// Published posts
$stmt = $db->prepare("SELECT COUNT(*) as total FROM posts WHERE status = 'published'");
$stmt->execute();
$stats['published_posts'] = $stmt->fetch()['total'];

// Draft posts
$stmt = $db->prepare("SELECT COUNT(*) as total FROM posts WHERE status = 'draft'");
$stmt->execute();
$stats['draft_posts'] = $stmt->fetch()['total'];

// Total categories
$stmt = $db->prepare("SELECT COUNT(*) as total FROM categories WHERE status = 'active'");
$stmt->execute();
$stats['total_categories'] = $stmt->fetch()['total'];

// Total users
$stmt = $db->prepare("SELECT COUNT(*) as total FROM users WHERE status = 'active'");
$stmt->execute();
$stats['total_users'] = $stmt->fetch()['total'];

// Total views today
$stmt = $db->prepare("SELECT COUNT(*) as total FROM post_views WHERE DATE(viewed_at) = CURDATE()");
$stmt->execute();
$stats['views_today'] = $stmt->fetch()['total'];

// Recent posts
$stmt = $db->prepare("SELECT p.*, c.name_bn as category_name, u.full_name as author_name 
                     FROM posts p 
                     JOIN categories c ON p.category_id = c.id 
                     JOIN users u ON p.author_id = u.id 
                     ORDER BY p.created_at DESC 
                     LIMIT 5");
$stmt->execute();
$recent_posts = $stmt->fetchAll();

// Popular posts this week
$stmt = $db->prepare("SELECT p.title_bn, p.view_count, c.name_bn as category_name
                     FROM posts p 
                     JOIN categories c ON p.category_id = c.id 
                     WHERE p.status = 'published' 
                     ORDER BY p.view_count DESC 
                     LIMIT 5");
$stmt->execute();
$popular_posts = $stmt->fetchAll();

$page_title = 'Dashboard';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Dashboard</h1>
                <div class="text-muted">
                    <i class="fas fa-clock me-1"></i>
                    <?= date('l, F j, Y - g:i A') ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?= number_format($stats['total_posts']) ?></div>
                            <div class="small">Total Posts</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-newspaper fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?= number_format($stats['published_posts']) ?></div>
                            <div class="small">Published</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?= number_format($stats['draft_posts']) ?></div>
                            <div class="small">Drafts</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-edit fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?= number_format($stats['total_categories']) ?></div>
                            <div class="small">Categories</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tags fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?= number_format($stats['total_users']) ?></div>
                            <div class="small">Users</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h4 mb-0"><?= number_format($stats['views_today']) ?></div>
                            <div class="small">Views Today</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-eye fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Posts -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Posts</h5>
                    <a href="posts.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($recent_posts)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No posts yet. <a href="posts.php?action=add">Create your first post</a></p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Author</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_posts as $post): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= e(truncateText($post['title_bn'], 50)) ?></div>
                                                <?php if ($post['is_featured']): ?>
                                                    <span class="badge bg-warning text-dark">Featured</span>
                                                <?php endif; ?>
                                                <?php if ($post['is_breaking']): ?>
                                                    <span class="badge bg-danger">Breaking</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge" style="background-color: <?= e($post['category_color'] ?? '#6c757d') ?>">
                                                    <?= e($post['category_name']) ?>
                                                </span>
                                            </td>
                                            <td><?= e($post['author_name']) ?></td>
                                            <td>
                                                <?php
                                                $status_class = [
                                                    'published' => 'success',
                                                    'draft' => 'warning',
                                                    'archived' => 'secondary'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $status_class[$post['status']] ?>">
                                                    <?= ucfirst($post['status']) ?>
                                                </span>
                                            </td>
                                            <td><?= timeAgo($post['created_at']) ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="posts.php?action=edit&id=<?= $post['id'] ?>" 
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($post['status'] === 'published'): ?>
                                                        <a href="<?= SITE_URL ?>/post.php?slug=<?= $post['slug'] ?>" 
                                                           class="btn btn-outline-success" title="View" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Popular Posts -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Popular Posts</h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($popular_posts)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No popular posts yet</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($popular_posts as $index => $post): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="me-auto">
                                        <div class="fw-bold"><?= $index + 1 ?>. <?= e(truncateText($post['title_bn'], 40)) ?></div>
                                        <small class="text-muted"><?= e($post['category_name']) ?></small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill"><?= number_format($post['view_count']) ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="posts.php?action=add" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                New Post
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="categories.php" class="btn btn-info w-100">
                                <i class="fas fa-tags me-2"></i>
                                Manage Categories
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="media.php" class="btn btn-success w-100">
                                <i class="fas fa-images me-2"></i>
                                Media Library
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="settings.php" class="btn btn-secondary w-100">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
