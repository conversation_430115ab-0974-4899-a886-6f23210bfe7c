<?php
/**
 * Newsletter Subscription AJAX Handler
 * Bengali News CMS
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit;
}

$email = cleanInput($_POST['email'] ?? '');

if (empty($email)) {
    $response['message'] = 'Email is required';
    echo json_encode($response);
    exit;
}

if (!validateEmail($email)) {
    $response['message'] = 'Invalid email address';
    echo json_encode($response);
    exit;
}

try {
    // Create newsletter subscribers table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS newsletter_subscribers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        unsubscribed_at TIMESTAMP NULL,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        INDEX idx_email (email),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    
    // Check if email already exists
    $stmt = $db->prepare("SELECT id, status FROM newsletter_subscribers WHERE email = ?");
    $stmt->execute([$email]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        if ($existing['status'] === 'active') {
            $response['message'] = 'This email is already subscribed';
        } else {
            // Reactivate subscription
            $stmt = $db->prepare("UPDATE newsletter_subscribers SET status = 'active', subscribed_at = NOW(), unsubscribed_at = NULL WHERE email = ?");
            $stmt->execute([$email]);
            $response['success'] = true;
            $response['message'] = 'Subscription reactivated successfully';
        }
    } else {
        // Add new subscriber
        $ip_address = getClientIP();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $stmt = $db->prepare("INSERT INTO newsletter_subscribers (email, ip_address, user_agent) VALUES (?, ?, ?)");
        $stmt->execute([$email, $ip_address, $user_agent]);
        
        $response['success'] = true;
        $response['message'] = 'Successfully subscribed to newsletter';
        
        // Log the subscription
        logSecurityEvent('newsletter_subscribe', "Email: $email");
    }
    
} catch (Exception $e) {
    $response['message'] = 'Subscription failed. Please try again.';
    error_log('Newsletter subscription error: ' . $e->getMessage());
}

echo json_encode($response);
?>
