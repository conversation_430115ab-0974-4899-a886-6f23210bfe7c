<?php
/**
 * Posts Management
 * Bengali News CMS
 */

require_once '../config/config.php';
requireLogin();

$action = $_GET['action'] ?? 'list';
$post_id = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token';
    } else {
        if ($action === 'add' || $action === 'edit') {
            $title = cleanInput($_POST['title']);
            $title_bn = cleanInput($_POST['title_bn']);
            $excerpt = cleanInput($_POST['excerpt']);
            $content = $_POST['content']; // Don't clean HTML content
            $category_id = (int)$_POST['category_id'];
            $status = cleanInput($_POST['status']);
            $is_featured = isset($_POST['is_featured']) ? 1 : 0;
            $is_breaking = isset($_POST['is_breaking']) ? 1 : 0;
            $meta_title = cleanInput($_POST['meta_title']);
            $meta_description = cleanInput($_POST['meta_description']);
            $meta_keywords = cleanInput($_POST['meta_keywords']);
            $tags = cleanInput($_POST['tags']);
            
            // Generate slug
            $slug = generateSlug($title_bn ?: $title);
            
            // Handle featured image upload
            $featured_image = '';
            if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = validateFileUpload($_FILES['featured_image'], ALLOWED_IMAGE_TYPES);
                if ($upload_result['success']) {
                    $filename = generateSecureFilename($_FILES['featured_image']['name']);
                    $upload_path = UPLOAD_PATH . 'images/' . $filename;
                    
                    if (move_uploaded_file($_FILES['featured_image']['tmp_name'], $upload_path)) {
                        $featured_image = 'images/' . $filename;
                        
                        // Save to media library
                        $stmt = $db->prepare("INSERT INTO media (filename, original_name, file_path, file_size, mime_type, uploaded_by) 
                                             VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->execute([
                            $filename,
                            $_FILES['featured_image']['name'],
                            $featured_image,
                            $_FILES['featured_image']['size'],
                            $upload_result['mime_type'],
                            $_SESSION['user_id']
                        ]);
                    }
                }
            }
            
            if ($action === 'add') {
                // Insert new post
                $published_at = ($status === 'published') ? date('Y-m-d H:i:s') : null;
                
                $sql = "INSERT INTO posts (title, title_bn, slug, excerpt, content, featured_image, category_id, author_id, 
                                         status, is_featured, is_breaking, meta_title, meta_description, meta_keywords, published_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $stmt = $db->prepare($sql);
                $result = $stmt->execute([
                    $title, $title_bn, $slug, $excerpt, $content, $featured_image, $category_id, $_SESSION['user_id'],
                    $status, $is_featured, $is_breaking, $meta_title, $meta_description, $meta_keywords, $published_at
                ]);
                
                if ($result) {
                    $new_post_id = $db->lastInsertId();
                    
                    // Handle tags
                    if (!empty($tags)) {
                        $tag_names = array_map('trim', explode(',', $tags));
                        foreach ($tag_names as $tag_name) {
                            if (!empty($tag_name)) {
                                $tag_slug = generateSlug($tag_name);
                                
                                // Insert or get tag
                                $stmt = $db->prepare("INSERT IGNORE INTO tags (name, name_bn, slug) VALUES (?, ?, ?)");
                                $stmt->execute([$tag_name, $tag_name, $tag_slug]);
                                
                                $stmt = $db->prepare("SELECT id FROM tags WHERE slug = ?");
                                $stmt->execute([$tag_slug]);
                                $tag = $stmt->fetch();
                                
                                if ($tag) {
                                    // Link post to tag
                                    $stmt = $db->prepare("INSERT IGNORE INTO post_tags (post_id, tag_id) VALUES (?, ?)");
                                    $stmt->execute([$new_post_id, $tag['id']]);
                                }
                            }
                        }
                    }
                    
                    $success = 'Post created successfully';
                    clearCacheByPattern('latest_posts_');
                    clearCacheByPattern('featured_posts_');
                } else {
                    $error = 'Failed to create post';
                }
                
            } elseif ($action === 'edit' && $post_id) {
                // Update existing post
                $current_post = null;
                $stmt = $db->prepare("SELECT * FROM posts WHERE id = ?");
                $stmt->execute([$post_id]);
                $current_post = $stmt->fetch();
                
                if (!$current_post) {
                    $error = 'Post not found';
                } elseif (!hasPermission('admin') && $current_post['author_id'] != $_SESSION['user_id']) {
                    $error = 'You can only edit your own posts';
                } else {
                    // Update published_at if status changed to published
                    $published_at = $current_post['published_at'];
                    if ($status === 'published' && $current_post['status'] !== 'published') {
                        $published_at = date('Y-m-d H:i:s');
                    }
                    
                    // Use existing featured image if no new one uploaded
                    if (empty($featured_image)) {
                        $featured_image = $current_post['featured_image'];
                    }
                    
                    $sql = "UPDATE posts SET title = ?, title_bn = ?, slug = ?, excerpt = ?, content = ?, 
                                           featured_image = ?, category_id = ?, status = ?, is_featured = ?, is_breaking = ?,
                                           meta_title = ?, meta_description = ?, meta_keywords = ?, published_at = ?, updated_at = NOW()
                            WHERE id = ?";
                    
                    $stmt = $db->prepare($sql);
                    $result = $stmt->execute([
                        $title, $title_bn, $slug, $excerpt, $content, $featured_image, $category_id,
                        $status, $is_featured, $is_breaking, $meta_title, $meta_description, $meta_keywords, 
                        $published_at, $post_id
                    ]);
                    
                    if ($result) {
                        // Update tags
                        $stmt = $db->prepare("DELETE FROM post_tags WHERE post_id = ?");
                        $stmt->execute([$post_id]);
                        
                        if (!empty($tags)) {
                            $tag_names = array_map('trim', explode(',', $tags));
                            foreach ($tag_names as $tag_name) {
                                if (!empty($tag_name)) {
                                    $tag_slug = generateSlug($tag_name);
                                    
                                    $stmt = $db->prepare("INSERT IGNORE INTO tags (name, name_bn, slug) VALUES (?, ?, ?)");
                                    $stmt->execute([$tag_name, $tag_name, $tag_slug]);
                                    
                                    $stmt = $db->prepare("SELECT id FROM tags WHERE slug = ?");
                                    $stmt->execute([$tag_slug]);
                                    $tag = $stmt->fetch();
                                    
                                    if ($tag) {
                                        $stmt = $db->prepare("INSERT IGNORE INTO post_tags (post_id, tag_id) VALUES (?, ?)");
                                        $stmt->execute([$post_id, $tag['id']]);
                                    }
                                }
                            }
                        }
                        
                        $success = 'Post updated successfully';
                        clearCacheByPattern('latest_posts_');
                        clearCacheByPattern('featured_posts_');
                    } else {
                        $error = 'Failed to update post';
                    }
                }
            }
        } elseif ($action === 'delete' && $post_id) {
            $stmt = $db->prepare("SELECT * FROM posts WHERE id = ?");
            $stmt->execute([$post_id]);
            $post = $stmt->fetch();
            
            if (!$post) {
                $error = 'Post not found';
            } elseif (!hasPermission('admin') && $post['author_id'] != $_SESSION['user_id']) {
                $error = 'You can only delete your own posts';
            } else {
                $stmt = $db->prepare("DELETE FROM posts WHERE id = ?");
                if ($stmt->execute([$post_id])) {
                    $success = 'Post deleted successfully';
                    clearCacheByPattern('latest_posts_');
                    clearCacheByPattern('featured_posts_');
                } else {
                    $error = 'Failed to delete post';
                }
            }
        }
    }
}

// Get categories for dropdown
$stmt = $db->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY name_bn");
$stmt->execute();
$categories = $stmt->fetchAll();

// Get post data for edit
$post_data = null;
$post_tags = [];
if ($action === 'edit' && $post_id) {
    $stmt = $db->prepare("SELECT * FROM posts WHERE id = ?");
    $stmt->execute([$post_id]);
    $post_data = $stmt->fetch();
    
    if ($post_data) {
        // Get post tags
        $stmt = $db->prepare("SELECT t.name FROM tags t JOIN post_tags pt ON t.id = pt.tag_id WHERE pt.post_id = ?");
        $stmt->execute([$post_id]);
        $post_tags = array_column($stmt->fetchAll(), 'name');
    }
}

$page_title = ($action === 'add') ? 'Add New Post' : (($action === 'edit') ? 'Edit Post' : 'Posts');
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($action === 'list'): ?>
        <!-- Posts List -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Posts</h1>
                    <a href="posts.php?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Post
                    </a>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <?php
                        // Get posts with pagination
                        $page = $_GET['page'] ?? 1;
                        $limit = ADMIN_POSTS_PER_PAGE;
                        $offset = ($page - 1) * $limit;
                        
                        $where_clause = '';
                        $params = [];
                        
                        // Filter by author for non-admin users
                        if (!hasPermission('admin')) {
                            $where_clause = 'WHERE p.author_id = ?';
                            $params[] = $_SESSION['user_id'];
                        }
                        
                        $sql = "SELECT p.*, c.name_bn as category_name, c.color as category_color, u.full_name as author_name
                                FROM posts p 
                                JOIN categories c ON p.category_id = c.id 
                                JOIN users u ON p.author_id = u.id 
                                $where_clause
                                ORDER BY p.created_at DESC 
                                LIMIT ? OFFSET ?";
                        
                        $params[] = $limit;
                        $params[] = $offset;
                        
                        $stmt = $db->prepare($sql);
                        $stmt->execute($params);
                        $posts = $stmt->fetchAll();
                        ?>
                        
                        <?php if (empty($posts)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                                <h5>No posts found</h5>
                                <p class="text-muted">Start by creating your first post</p>
                                <a href="posts.php?action=add" class="btn btn-primary">Add New Post</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Title</th>
                                            <th>Category</th>
                                            <th>Author</th>
                                            <th>Status</th>
                                            <th>Views</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($posts as $post): ?>
                                            <tr>
                                                <td>
                                                    <div class="fw-bold"><?= e(truncateText($post['title_bn'], 50)) ?></div>
                                                    <div class="small text-muted"><?= e(truncateText($post['title'], 40)) ?></div>
                                                    <?php if ($post['is_featured']): ?>
                                                        <span class="badge bg-warning text-dark">Featured</span>
                                                    <?php endif; ?>
                                                    <?php if ($post['is_breaking']): ?>
                                                        <span class="badge bg-danger">Breaking</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge" style="background-color: <?= e($post['category_color']) ?>">
                                                        <?= e($post['category_name']) ?>
                                                    </span>
                                                </td>
                                                <td><?= e($post['author_name']) ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = [
                                                        'published' => 'success',
                                                        'draft' => 'warning',
                                                        'archived' => 'secondary'
                                                    ];
                                                    ?>
                                                    <span class="badge bg-<?= $status_class[$post['status']] ?>">
                                                        <?= ucfirst($post['status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= number_format($post['view_count']) ?></td>
                                                <td><?= timeAgo($post['created_at']) ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="posts.php?action=edit&id=<?= $post['id'] ?>" 
                                                           class="btn btn-outline-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <?php if ($post['status'] === 'published'): ?>
                                                            <a href="<?= SITE_URL ?>/post.php?slug=<?= $post['slug'] ?>" 
                                                               class="btn btn-outline-success" title="View" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if (hasPermission('admin') || $post['author_id'] == $_SESSION['user_id']): ?>
                                                            <a href="posts.php?action=delete&id=<?= $post['id'] ?>" 
                                                               class="btn btn-outline-danger" title="Delete"
                                                               onclick="return confirmDelete('Are you sure you want to delete this post?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Add/Edit Post Form -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><?= $action === 'add' ? 'Add New Post' : 'Edit Post' ?></h1>
                    <a href="posts.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Posts
                    </a>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= e($error) ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?= e($success) ?></div>
                <?php endif; ?>
                
                <form method="POST" enctype="multipart/form-data" id="postForm">
                    <?= getCSRFTokenInput() ?>
                    
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Post Content</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="title_bn" class="form-label">Title (Bengali) *</label>
                                        <input type="text" class="form-control" id="title_bn" name="title_bn" 
                                               value="<?= e($post_data['title_bn'] ?? '') ?>" required
                                               onkeyup="generateSlug(this.value, 'slug_preview')">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title (English)</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="<?= e($post_data['title'] ?? '') ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">URL Slug</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?= SITE_URL ?>/post/</span>
                                            <input type="text" class="form-control" id="slug_preview" readonly
                                                   value="<?= e($post_data['slug'] ?? '') ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="excerpt" class="form-label">Excerpt</label>
                                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3"
                                                  placeholder="Brief description of the post"><?= e($post_data['excerpt'] ?? '') ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="content" class="form-label">Content *</label>
                                        <textarea class="form-control summernote" id="content" name="content" required><?= $post_data['content'] ?? '' ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- SEO Settings -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">SEO Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="meta_title" class="form-label">Meta Title</label>
                                        <input type="text" class="form-control" id="meta_title" name="meta_title" 
                                               value="<?= e($post_data['meta_title'] ?? '') ?>"
                                               maxlength="60" onkeyup="updateCharCount('meta_title', 'meta_title_count', 60)">
                                        <div class="form-text" id="meta_title_count">60 characters remaining</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="meta_description" class="form-label">Meta Description</label>
                                        <textarea class="form-control" id="meta_description" name="meta_description" rows="3"
                                                  maxlength="160" onkeyup="updateCharCount('meta_description', 'meta_desc_count', 160)"><?= e($post_data['meta_description'] ?? '') ?></textarea>
                                        <div class="form-text" id="meta_desc_count">160 characters remaining</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" 
                                               value="<?= e($post_data['meta_keywords'] ?? '') ?>"
                                               placeholder="keyword1, keyword2, keyword3">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <!-- Publish Settings -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Publish Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="draft" <?= ($post_data['status'] ?? '') === 'draft' ? 'selected' : '' ?>>Draft</option>
                                            <option value="published" <?= ($post_data['status'] ?? '') === 'published' ? 'selected' : '' ?>>Published</option>
                                            <option value="archived" <?= ($post_data['status'] ?? '') === 'archived' ? 'selected' : '' ?>>Archived</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Category *</label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= $category['id'] ?>" 
                                                        <?= ($post_data['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                                                    <?= e($category['name_bn']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="tags" class="form-label">Tags</label>
                                        <input type="text" class="form-control" id="tags" name="tags" 
                                               value="<?= e(implode(', ', $post_tags)) ?>"
                                               placeholder="tag1, tag2, tag3">
                                        <div class="form-text">Separate tags with commas</div>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                               <?= ($post_data['is_featured'] ?? 0) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_featured">
                                            Featured Post
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="is_breaking" name="is_breaking" 
                                               <?= ($post_data['is_breaking'] ?? 0) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_breaking">
                                            Breaking News
                                        </label>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            <?= $action === 'add' ? 'Create Post' : 'Update Post' ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Featured Image -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Featured Image</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($post_data['featured_image'])): ?>
                                        <div class="mb-3">
                                            <img src="<?= UPLOAD_URL . $post_data['featured_image'] ?>" 
                                                 class="img-fluid rounded" alt="Current featured image">
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <input type="file" class="form-control" id="featured_image" name="featured_image" 
                                               accept="image/*" onchange="previewImage(this, 'image_preview')">
                                    </div>
                                    
                                    <img id="image_preview" class="img-fluid rounded" style="display: none;" alt="Preview">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
$custom_scripts = "
<script>
    // Enable auto-save for post form
    if (document.getElementById('postForm')) {
        enableAutoSave('postForm');
    }
    
    // Initialize character counters
    updateCharCount('meta_title', 'meta_title_count', 60);
    updateCharCount('meta_description', 'meta_desc_count', 160);
</script>
";

include 'includes/footer.php';
?>
