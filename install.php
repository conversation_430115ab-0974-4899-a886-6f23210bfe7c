<?php
/**
 * Installation Script
 * Bengali News CMS
 */

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('CMS is already installed. Delete config/installed.lock to reinstall.');
}

require_once 'config/config.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Test database connection
        $database = new Database();
        $db = $database->connect();
        
        if (!$db) {
            throw new Exception('Could not connect to database');
        }
        
        // Create tables
        if (!$database->createTables()) {
            throw new Exception('Could not create database tables');
        }
        
        // Insert default data
        if (!$database->insertDefaultData()) {
            throw new Exception('Could not insert default data');
        }
        
        // Create admin user with custom credentials
        $admin_username = cleanInput($_POST['admin_username']);
        $admin_email = cleanInput($_POST['admin_email']);
        $admin_password = $_POST['admin_password'];
        $admin_name = cleanInput($_POST['admin_name']);
        
        if (empty($admin_username) || empty($admin_email) || empty($admin_password) || empty($admin_name)) {
            throw new Exception('All admin fields are required');
        }
        
        if (!validateEmail($admin_email)) {
            throw new Exception('Invalid email address');
        }
        
        if (!validatePassword($admin_password)) {
            throw new Exception('Password must be at least 8 characters with uppercase, lowercase, and number');
        }
        
        // Delete default admin and create new one
        $stmt = $db->prepare("DELETE FROM users WHERE username = 'admin'");
        $stmt->execute();
        
        $hashed_password = hashPassword($admin_password);
        $stmt = $db->prepare("INSERT INTO users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, 'admin')");
        $stmt->execute([$admin_username, $admin_email, $hashed_password, $admin_name]);
        
        // Update site settings
        $site_title = cleanInput($_POST['site_title']) ?: 'Bengali News CMS';
        $site_description = cleanInput($_POST['site_description']) ?: 'A modern Bengali news website';
        
        setSetting('site_title', $site_title);
        setSetting('site_description', $site_description);
        
        // Create installation lock file
        file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
        
        $success = 'Installation completed successfully! You can now login to the admin panel.';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Bengali News CMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .card { box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .btn-primary { background-color: #28a745; border-color: #28a745; }
        .btn-primary:hover { background-color: #218838; border-color: #1e7e34; }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="card">
                <div class="card-header bg-primary text-white text-center">
                    <h3>Bengali News CMS Installation</h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?= e($error) ?></div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success"><?= e($success) ?></div>
                        <div class="text-center">
                            <a href="admin/login.php" class="btn btn-primary">Go to Admin Login</a>
                        </div>
                    <?php else: ?>
                        <form method="POST">
                            <h5 class="mb-3">Database Configuration</h5>
                            <div class="alert alert-info">
                                <small>Make sure your database exists and credentials are correct in config/config.php</small>
                            </div>
                            
                            <h5 class="mb-3 mt-4">Admin Account</h5>
                            <div class="mb-3">
                                <label for="admin_username" class="form-label">Admin Username</label>
                                <input type="text" class="form-control" id="admin_username" name="admin_username" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_email" class="form-label">Admin Email</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">Admin Password</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                                <div class="form-text">At least 8 characters with uppercase, lowercase, and number</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_name" class="form-label">Admin Full Name</label>
                                <input type="text" class="form-control" id="admin_name" name="admin_name" required>
                            </div>
                            
                            <h5 class="mb-3 mt-4">Site Configuration</h5>
                            <div class="mb-3">
                                <label for="site_title" class="form-label">Site Title</label>
                                <input type="text" class="form-control" id="site_title" name="site_title" value="Bengali News CMS">
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_description" class="form-label">Site Description</label>
                                <textarea class="form-control" id="site_description" name="site_description" rows="3">A modern Bengali news website</textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">Install CMS</button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <small class="text-muted">Bengali News CMS v1.0 - Built with PHP & MySQL</small>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
