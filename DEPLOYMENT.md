# Deployment Guide - Bengali News CMS

This guide will help you deploy the Bengali News CMS to a production server.

## Pre-Deployment Checklist

### Server Requirements
- [ ] PHP 7.4 or higher
- [ ] MySQL 5.7 or higher (or MariaDB 10.2+)
- [ ] Apache/Nginx web server
- [ ] mod_rewrite enabled (Apache) or equivalent (Nginx)
- [ ] SSL certificate installed
- [ ] Required PHP extensions:
  - [ ] PDO MySQL
  - [ ] GD
  - [ ] mbstring
  - [ ] curl
  - [ ] zip

### File Preparation
- [ ] Download/clone the CMS files
- [ ] Create upload directories
- [ ] Set proper file permissions
- [ ] Configure web server

## Step-by-Step Deployment

### 1. Upload Files
Upload all CMS files to your web server's document root or subdirectory.

### 2. Create Directories
Create the following directories with write permissions:
```bash
mkdir -p uploads/images
mkdir -p uploads/documents
mkdir -p uploads/categories
mkdir -p cache
mkdir -p config
```

### 3. Set File Permissions
```bash
# For Apache/Nginx user (usually www-data)
chown -R www-data:www-data /path/to/your/cms
chmod -R 755 /path/to/your/cms
chmod -R 775 uploads/
chmod -R 775 cache/
chmod 775 config/
```

### 4. Database Setup
1. Create a MySQL database
2. Create a database user with full privileges on the database
3. Note down the database credentials

### 5. Web Server Configuration

#### Apache (.htaccess)
Create `.htaccess` in the root directory:
```apache
RewriteEngine On

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Hide sensitive files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.lock">
    Order allow,deny
    Deny from all
</Files>

# Clean URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^post/([^/]+)/?$ post.php?slug=$1 [L,QSA]
RewriteRule ^category/([^/]+)/?$ category.php?slug=$1 [L,QSA]
RewriteRule ^tag/([^/]+)/?$ tag.php?slug=$1 [L,QSA]
RewriteRule ^page/([^/]+)/?$ page.php?slug=$1 [L,QSA]

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

#### Nginx Configuration
Add to your Nginx server block:
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name your-domain.com;
    root /path/to/your/cms;
    index index.php;

    # SSL configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Hide sensitive files
    location ~ /config/ {
        deny all;
    }
    
    location ~ \.lock$ {
        deny all;
    }

    # Clean URLs
    location / {
        try_files $uri $uri/ @rewrite;
    }

    location @rewrite {
        rewrite ^/post/([^/]+)/?$ /post.php?slug=$1 last;
        rewrite ^/category/([^/]+)/?$ /category.php?slug=$1 last;
        rewrite ^/tag/([^/]+)/?$ /tag.php?slug=$1 last;
        rewrite ^/page/([^/]+)/?$ /page.php?slug=$1 last;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    # Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

### 6. Run Installation
1. Navigate to `https://your-domain.com/install.php`
2. Follow the installation wizard
3. Enter database credentials
4. Create admin account
5. Complete installation

### 7. Post-Installation Security

#### Remove Installation Files
```bash
rm install.php
rm install_default_pages.php
```

#### Secure Configuration
1. Move `config/config.php` outside web root (optional but recommended)
2. Update the include path in other files if moved
3. Set restrictive permissions on config file:
```bash
chmod 600 config/config.php
```

#### Setup Cron Jobs
Add these cron jobs for optimal performance:
```bash
# Update popular posts ranking every hour
0 * * * * /usr/bin/php /path/to/your/cms/cron/update_popular_posts.php

# Clean up cache daily at 2 AM
0 2 * * * /usr/bin/php /path/to/your/cms/cron/cleanup_cache.php
```

### 8. SSL and Security

#### Force HTTPS
Add to your `.htaccess` (Apache) or Nginx config:
```apache
# Apache
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

```nginx
# Nginx
if ($scheme != "https") {
    return 301 https://$server_name$request_uri;
}
```

#### Additional Security Measures
- [ ] Change default admin username
- [ ] Use strong passwords
- [ ] Enable fail2ban for brute force protection
- [ ] Regular security updates
- [ ] Monitor security logs
- [ ] Setup automated backups

### 9. Performance Optimization

#### Enable Caching
1. Verify cache directory is writable
2. Enable caching in admin settings
3. Configure cache TTL values

#### Database Optimization
```sql
-- Add these indexes for better performance
ALTER TABLE posts ADD INDEX idx_published_featured (published_at, is_featured);
ALTER TABLE posts ADD INDEX idx_category_published (category_id, published_at);
ALTER TABLE post_views ADD INDEX idx_post_viewed (post_id, viewed_at);
```

#### CDN Setup (Optional)
1. Configure CDN for static assets
2. Update UPLOAD_URL in config
3. Test image loading

### 10. Backup Strategy

#### Database Backup
```bash
#!/bin/bash
# Daily database backup
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql
```

#### File Backup
```bash
#!/bin/bash
# Daily file backup
tar -czf backup_files_$(date +%Y%m%d).tar.gz /path/to/your/cms --exclude='cache/*'
```

### 11. Monitoring

#### Log Files to Monitor
- Web server error logs
- PHP error logs
- Application security logs
- Database slow query logs

#### Performance Monitoring
- Page load times
- Database query performance
- Server resource usage
- Cache hit rates

### 12. Maintenance

#### Regular Tasks
- [ ] Update CMS when new versions are available
- [ ] Monitor and clean up old cache entries
- [ ] Review and clean up old media files
- [ ] Monitor database size and optimize
- [ ] Review security logs
- [ ] Test backup restoration

#### Monthly Tasks
- [ ] Review user accounts and permissions
- [ ] Update SSL certificates if needed
- [ ] Performance audit
- [ ] Security audit

## Troubleshooting

### Common Issues

#### Permission Errors
```bash
# Fix file permissions
find /path/to/cms -type f -exec chmod 644 {} \;
find /path/to/cms -type d -exec chmod 755 {} \;
chmod -R 775 uploads/ cache/
```

#### Database Connection Issues
1. Verify database credentials
2. Check database server status
3. Verify PHP PDO MySQL extension
4. Check firewall settings

#### Performance Issues
1. Enable caching
2. Optimize database queries
3. Check server resources
4. Review slow query logs

#### Security Issues
1. Review security logs
2. Check file permissions
3. Verify SSL configuration
4. Update passwords

## Support

For deployment support:
- Documentation: [Link to docs]
- Community Forum: [Link to forum]
- Email Support: <EMAIL>

---

**Remember**: Always test your deployment on a staging server before going live!
