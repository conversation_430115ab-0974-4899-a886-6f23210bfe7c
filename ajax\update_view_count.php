<?php
/**
 * Update View Count AJAX Handler
 * Bengali News CMS
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$response = ['success' => false, 'view_count' => 0];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit;
}

$post_id = (int)($_POST['post_id'] ?? 0);

if (!$post_id) {
    $response['message'] = 'Post ID is required';
    echo json_encode($response);
    exit;
}

try {
    // Check if post exists
    $stmt = $db->prepare("SELECT id, view_count FROM posts WHERE id = ? AND status = 'published'");
    $stmt->execute([$post_id]);
    $post = $stmt->fetch();
    
    if (!$post) {
        $response['message'] = 'Post not found';
        echo json_encode($response);
        exit;
    }
    
    $ip_address = getClientIP();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Check if this IP has viewed this post in the last hour
    $sql = "SELECT id FROM post_views 
            WHERE post_id = ? AND ip_address = ? AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$post_id, $ip_address]);
    
    if (!$stmt->fetch()) {
        // Record new view
        $sql = "INSERT INTO post_views (post_id, ip_address, user_agent) VALUES (?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$post_id, $ip_address, $user_agent]);
        
        // Update post view count
        $sql = "UPDATE posts SET view_count = view_count + 1 WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$post_id]);
        
        // Get updated view count
        $stmt = $db->prepare("SELECT view_count FROM posts WHERE id = ?");
        $stmt->execute([$post_id]);
        $updated_post = $stmt->fetch();
        
        $response['success'] = true;
        $response['view_count'] = $updated_post['view_count'];
        $response['message'] = 'View recorded';
        
        // Clear popular posts cache
        clearCacheByPattern('popular_posts_');
        clearCacheByPattern('trending_posts_');
        
    } else {
        // Return current view count without incrementing
        $response['success'] = true;
        $response['view_count'] = $post['view_count'];
        $response['message'] = 'View already recorded';
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?>
