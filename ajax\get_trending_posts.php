<?php
/**
 * Get Trending Posts AJAX Handler
 * Bengali News CMS
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$response = ['success' => false, 'posts' => []];

$limit = (int)($_GET['limit'] ?? 5);
$hours = (int)($_GET['hours'] ?? 24); // Trending in last 24 hours

try {
    $cache_key = "trending_posts_{$limit}_{$hours}";
    $cached = getCache($cache_key);
    
    if ($cached) {
        $response['success'] = true;
        $response['posts'] = $cached;
    } else {
        $date_from = date('Y-m-d H:i:s', strtotime("-$hours hours"));
        
        $sql = "SELECT p.*, c.name_bn as category_name, c.slug as category_slug, c.color as category_color,
                       u.full_name as author_name,
                       COUNT(pv.id) as recent_views,
                       p.view_count as total_views
                FROM posts p 
                JOIN categories c ON p.category_id = c.id 
                JOIN users u ON p.author_id = u.id 
                LEFT JOIN post_views pv ON p.id = pv.post_id AND pv.viewed_at >= ?
                WHERE p.status = 'published' 
                GROUP BY p.id
                HAVING recent_views > 0
                ORDER BY recent_views DESC, p.view_count DESC
                LIMIT ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$date_from, $limit]);
        $posts = $stmt->fetchAll();
        
        // Format posts for JSON response
        $formatted_posts = [];
        foreach ($posts as $post) {
            $formatted_posts[] = [
                'id' => $post['id'],
                'title_bn' => $post['title_bn'],
                'slug' => $post['slug'],
                'featured_image' => $post['featured_image'] ? UPLOAD_URL . $post['featured_image'] : null,
                'category_name' => $post['category_name'],
                'category_slug' => $post['category_slug'],
                'category_color' => $post['category_color'],
                'author_name' => $post['author_name'],
                'published_at' => $post['published_at'],
                'view_count' => $post['view_count'],
                'recent_views' => $post['recent_views'],
                'time_ago' => timeAgo($post['published_at']),
                'url' => SITE_URL . '/post.php?slug=' . $post['slug']
            ];
        }
        
        setCache($cache_key, $formatted_posts, 900); // Cache for 15 minutes
        
        $response['success'] = true;
        $response['posts'] = $formatted_posts;
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?>
