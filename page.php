<?php
/**
 * Single Page Display - Bengali News CMS
 */

require_once 'config/config.php';

$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Get page by slug
$page = getPageBySlug($slug);

if (!$page) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Set page meta data
$page_title = $page['meta_title'] ?: $page['title_bn'];
$page_description = $page['meta_description'] ?: truncateText(strip_tags($page['content']), 160);

include 'includes/frontend_header.php';
?>

<div class="container my-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= SITE_URL ?>">হোম</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= e($page['title_bn']) ?></li>
                </ol>
            </nav>
            
            <!-- Page Content -->
            <article class="page-content">
                <header class="page-header mb-4">
                    <h1 class="page-title"><?= e($page['title_bn']) ?></h1>
                    <?php if ($page['title']): ?>
                        <p class="page-subtitle text-muted"><?= e($page['title']) ?></p>
                    <?php endif; ?>
                    
                    <div class="page-meta text-muted">
                        <small>
                            <i class="fas fa-calendar-alt me-1"></i>
                            সর্বশেষ আপডেট: <?= formatDate($page['updated_at'] ?: $page['created_at'], 'F j, Y') ?>
                        </small>
                    </div>
                </header>
                
                <div class="page-body">
                    <?= $page['content'] ?>
                </div>
                
                <!-- Social Share -->
                <div class="page-share mt-5 pt-4 border-top">
                    <h6 class="mb-3">এই পৃষ্ঠাটি শেয়ার করুন:</h6>
                    <div class="social-share">
                        <button class="btn btn-primary btn-sm me-2" 
                                onclick="shareOnSocial('facebook', '<?= SITE_URL ?>/page.php?slug=<?= $page['slug'] ?>', '<?= e($page['title_bn']) ?>')">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </button>
                        <button class="btn btn-info btn-sm me-2" 
                                onclick="shareOnSocial('twitter', '<?= SITE_URL ?>/page.php?slug=<?= $page['slug'] ?>', '<?= e($page['title_bn']) ?>')">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </button>
                        <button class="btn btn-success btn-sm me-2" 
                                onclick="shareOnSocial('whatsapp', '<?= SITE_URL ?>/page.php?slug=<?= $page['slug'] ?>', '<?= e($page['title_bn']) ?>')">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </button>
                        <button class="btn btn-secondary btn-sm" 
                                onclick="copyToClipboard('<?= SITE_URL ?>/page.php?slug=<?= $page['slug'] ?>')">
                            <i class="fas fa-copy me-1"></i>Copy Link
                        </button>
                    </div>
                </div>
            </article>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Other Pages -->
            <?php 
            $other_pages = getPages();
            $other_pages = array_filter($other_pages, function($p) use ($page) {
                return $p['id'] !== $page['id'];
            });
            
            if (!empty($other_pages)): 
            ?>
                <div class="sidebar-widget mb-4">
                    <h4 class="widget-title">
                        <i class="fas fa-file-alt text-info me-2"></i>অন্যান্য পৃষ্ঠা
                    </h4>
                    <div class="pages-list">
                        <?php foreach ($other_pages as $other_page): ?>
                            <a href="page.php?slug=<?= $other_page['slug'] ?>" 
                               class="page-item d-flex align-items-center justify-content-between text-decoration-none mb-2 p-3 rounded">
                                <div>
                                    <div class="fw-medium"><?= e($other_page['title_bn']) ?></div>
                                    <?php if ($other_page['title']): ?>
                                        <small class="text-muted"><?= e($other_page['title']) ?></small>
                                    <?php endif; ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Latest Posts -->
            <?php 
            $latest_posts = getLatestPosts(5);
            if (!empty($latest_posts)): 
            ?>
                <div class="sidebar-widget mb-4">
                    <h4 class="widget-title">
                        <i class="fas fa-newspaper text-primary me-2"></i>সর্বশেষ সংবাদ
                    </h4>
                    <div class="latest-posts">
                        <?php foreach ($latest_posts as $latest): ?>
                            <div class="latest-post-item mb-3">
                                <div class="d-flex">
                                    <?php if ($latest['featured_image']): ?>
                                        <div class="latest-image me-3">
                                            <img src="<?= UPLOAD_URL . $latest['featured_image'] ?>" 
                                                 class="rounded" width="80" height="60" 
                                                 style="object-fit: cover;" alt="<?= e($latest['title_bn']) ?>">
                                        </div>
                                    <?php endif; ?>
                                    <div class="latest-content flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="post.php?slug=<?= $latest['slug'] ?>" 
                                               class="text-decoration-none">
                                                <?= e(truncateText($latest['title_bn'], 60)) ?>
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i><?= timeAgo($latest['published_at']) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Categories -->
            <div class="sidebar-widget mb-4">
                <h4 class="widget-title">
                    <i class="fas fa-tags text-success me-2"></i>বিভাগসমূহ
                </h4>
                <div class="categories-list">
                    <?php 
                    $categories = getCategories();
                    foreach ($categories as $category): 
                    ?>
                        <a href="category.php?slug=<?= $category['slug'] ?>" 
                           class="category-item d-flex align-items-center justify-content-between text-decoration-none mb-2 p-3 rounded">
                            <div class="d-flex align-items-center">
                                <?php if ($category['icon']): ?>
                                    <img src="<?= UPLOAD_URL . $category['icon'] ?>" 
                                         width="24" height="24" class="me-2" alt="<?= e($category['name_bn']) ?>">
                                <?php else: ?>
                                    <div class="category-color-box me-2" 
                                         style="width: 24px; height: 24px; background-color: <?= e($category['color']) ?>; border-radius: 4px;"></div>
                                <?php endif; ?>
                                <span class="fw-medium"><?= e($category['name_bn']) ?></span>
                            </div>
                            <i class="fas fa-chevron-right text-muted"></i>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Contact Info (if this is contact page) -->
            <?php if (strtolower($page['slug']) === 'contact' || strpos(strtolower($page['title_bn']), 'যোগাযোগ') !== false): ?>
                <div class="sidebar-widget mb-4">
                    <div class="contact-widget bg-primary text-white p-4 rounded">
                        <h4 class="widget-title text-white">
                            <i class="fas fa-phone me-2"></i>যোগাযোগের তথ্য
                        </h4>
                        <div class="contact-info">
                            <p class="mb-2">
                                <i class="fas fa-envelope me-2"></i>
                                <EMAIL>
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-phone me-2"></i>
                                +880 1234 567890
                            </p>
                            <p class="mb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                ঢাকা, বাংলাদেশ
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                সকাল ৯টা - সন্ধ্যা ৬টা
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.page-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.page-body {
    color: #444;
}

.page-body h1, .page-body h2, .page-body h3, 
.page-body h4, .page-body h5, .page-body h6 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.page-body p {
    margin-bottom: 1.5rem;
}

.page-body ul, .page-body ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.page-body blockquote {
    border-left: 4px solid #28a745;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #666;
}

.page-body img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
}

.page-item {
    background: #f8f9fa;
    transition: all 0.3s ease;
    color: inherit;
}

.page-item:hover {
    background: #e9ecef;
    color: inherit;
    transform: translateX(5px);
}

.contact-widget {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

@media (max-width: 768px) {
    .page-content {
        font-size: 1rem;
    }
    
    .page-title {
        font-size: 1.8rem;
    }
}
</style>

<?php
$custom_scripts = "
<script>
    // Copy to clipboard function
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            alert('লিংক কপি হয়েছে!');
        });
    }
    
    // Social sharing functions
    function shareOnSocial(platform, url, title) {
        let shareUrl = '';
        
        switch (platform) {
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=\${encodeURIComponent(url)}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?url=\${encodeURIComponent(url)}&text=\${encodeURIComponent(title)}`;
                break;
            case 'whatsapp':
                shareUrl = `https://wa.me/?text=\${encodeURIComponent(title + ' ' + url)}`;
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    }
</script>
";

include 'includes/frontend_footer.php';
?>
